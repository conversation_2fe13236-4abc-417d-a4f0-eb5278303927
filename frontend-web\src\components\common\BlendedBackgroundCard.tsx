import React from 'react';
import { Card, CardProps, alpha, Box, useTheme } from '@mui/material';
import { createBlendedBackground } from '../../utils/localImages';
import { useThemeContext } from '../../contexts/ThemeContext';
import { getSelectableStyles } from '../../utils/selectionUtils';

interface BlendedBackgroundCardProps extends Omit<CardProps, 'title' | 'content'> {
  backgroundImage?: string;
  primaryColor?: string;
  secondaryColor?: string;
  opacity?: number;
  height?: number | string;
  title?: React.ReactNode;
  content?: React.ReactNode;
  action?: React.ReactNode;
  footer?: React.ReactNode;
  module?: string;
}

/**
 * A Card component with a blended background image and gradient overlay
 * This component is designed to match the marketplace product cards
 */
const BlendedBackgroundCard: React.FC<BlendedBackgroundCardProps> = ({
  backgroundImage,
  primaryColor,
  secondaryColor,
  opacity = 0.7,
  height = 180,
  title,
  content,
  action,
  footer,
  module,
  children,
  sx = {},
  ...props
}) => {
  const theme = useTheme();
  const { currentColor, availableColors } = useThemeContext();
  const themeColor = availableColors[currentColor];

  // Get module color if module is provided
  const getModuleColor = () => {
    switch (module) {
      case 'animals':
        return theme.palette.primary.main;
      case 'breeding':
        return theme.palette.secondary.main;
      case 'health':
        return theme.palette.success.main;
      case 'feeding':
        return theme.palette.warning.main;
      case 'financial':
        return theme.palette.info.main;
      case 'reports':
        return theme.palette.error.main;
      default:
        return primaryColor || themeColor.primary;
    }
  };

  // Use module colors if module is provided, otherwise use provided colors or theme colors
  const cardPrimaryColor = module ? getModuleColor() : (primaryColor || themeColor.primary);
  const cardSecondaryColor = module ? (secondaryColor || theme.palette.primary.dark) : (secondaryColor || themeColor.secondary);

  return (
    <Card
      sx={{
        height: height,
        display: 'flex',
        flexDirection: 'column',
        borderRadius: '12px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
        overflow: 'hidden',
        border: `1px solid ${alpha(cardPrimaryColor, 0.1)}`,
        transition: 'transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease',
        backgroundColor: 'transparent',
        background: `linear-gradient(135deg,
          ${alpha(theme.palette.background.paper, 0.6)},
          ${alpha(theme.palette.background.paper, 0.4)})`,
        backdropFilter: 'blur(10px)',
        '&:hover': {
          transform: 'translateY(-5px)',
          boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
          borderColor: alpha(cardPrimaryColor, 0.3)
        },
        position: 'relative',
        zIndex: 1,
        cursor: 'pointer',
        pointerEvents: 'auto',
        ...sx,
      }}
      {...props}
    >
      {/* Background Image with Gradient Overlay */}
      <Box
        sx={{
          position: 'relative',
          height: '100%',
          backgroundImage: backgroundImage ? `url(${backgroundImage})` : 'none',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          '&::after': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: backgroundImage
              ? `linear-gradient(135deg, ${alpha(cardPrimaryColor, 0.65)}, ${alpha(cardSecondaryColor, 0.7)})`
              : `linear-gradient(135deg, ${alpha(cardPrimaryColor, 0.1)}, ${alpha(cardSecondaryColor, 0.15)})`,
            backdropFilter: 'blur(2px)',
            zIndex: 1
          }
        }}
      >
        {/* Content Container */}
        <Box
          sx={{
            position: 'relative',
            zIndex: 2,
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            p: 2,
            color: 'white',
            ...getSelectableStyles()
          }}
        >
          {title && (
            <Box mb={1}>{title}</Box>
          )}

          {content && (
            <Box sx={{ flex: 1 }}>{content}</Box>
          )}

          {children && (
            <Box sx={{ flex: 1 }}>{children}</Box>
          )}

          {action && (
            <Box mt="auto" pt={1}>{action}</Box>
          )}

          {footer && (
            <Box mt={1}>{footer}</Box>
          )}
        </Box>
      </Box>
    </Card>
  );
};

export default BlendedBackgroundCard;
