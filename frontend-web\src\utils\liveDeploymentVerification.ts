/**
 * LIVE Deployment Verification Suite
 * Comprehensive verification for production readiness
 */

export interface LiveDeploymentCheck {
  apiEndpoints: {
    animals: boolean;
    health: boolean;
    feeding: boolean;
    financial: boolean;
    settings: boolean;
    allWorking: boolean;
  };
  dataIntegrity: {
    betaAnimalsCount: number;
    healthRecordsCount: number;
    feedingRecordsCount: number;
    financialRecordsCount: number;
    relationshipsValid: boolean;
  };
  visualConsistency: {
    themeApplied: boolean;
    glassmorphismEffects: boolean;
    backgroundsVisible: boolean;
    noWhiteBackgrounds: boolean;
  };
  functionalElements: {
    formsWorking: boolean;
    buttonsClickable: boolean;
    navigationSmooth: boolean;
    crudOperations: boolean;
  };
  betaCompliance: {
    limitEnforced: boolean;
    upgradePrompts: boolean;
    restrictedModulesBlocked: boolean;
  };
  readyForLive: boolean;
  issues: string[];
}

const API_BASE_URL = 'http://localhost:3002/api';

/**
 * Verify all BETA API endpoints
 */
async function verifyApiEndpoints(): Promise<any> {
  const endpoints = [
    { name: 'animals', url: '/animals' },
    { name: 'health', url: '/health/records' },
    { name: 'feeding', url: '/feeding/records' },
    { name: 'financial', url: '/financial/records' },
    { name: 'settings', url: '/settings' }
  ];

  const results: any = {};
  let workingCount = 0;

  for (const endpoint of endpoints) {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint.url}`);
      const isWorking = response.ok;
      results[endpoint.name] = isWorking;
      
      if (isWorking) {
        workingCount++;
        console.log(`✅ ${endpoint.name} API: Working`);
      } else {
        console.log(`❌ ${endpoint.name} API: Failed (${response.status})`);
      }
    } catch (error) {
      results[endpoint.name] = false;
      console.log(`❌ ${endpoint.name} API: Error - ${error instanceof Error ? error.message : 'Unknown'}`);
    }
  }

  results.allWorking = workingCount === endpoints.length;
  return results;
}

/**
 * Verify data integrity
 */
async function verifyDataIntegrity(): Promise<any> {
  const result = {
    betaAnimalsCount: 0,
    healthRecordsCount: 0,
    feedingRecordsCount: 0,
    financialRecordsCount: 0,
    relationshipsValid: false
  };

  try {
    // Check animals
    const animalsResponse = await fetch(`${API_BASE_URL}/animals`);
    if (animalsResponse.ok) {
      const animalsData = await animalsResponse.json();
      const animals = animalsData.animals || animalsData;
      const betaAnimals = Array.isArray(animals) ? animals.filter((a: any) => 
        a.tagNumber?.startsWith('BETA-') || a.name === 'Tshepiso' || a.name === 'Lerato'
      ) : [];
      result.betaAnimalsCount = betaAnimals.length;
    }

    // Check health records
    const healthResponse = await fetch(`${API_BASE_URL}/health/records`);
    if (healthResponse.ok) {
      const healthData = await healthResponse.json();
      const healthRecords = healthData.records || healthData;
      result.healthRecordsCount = Array.isArray(healthRecords) ? healthRecords.filter((h: any) =>
        h.animalName === 'Tshepiso' || h.animalName === 'Lerato'
      ).length : 0;
    }

    // Check feeding records
    const feedingResponse = await fetch(`${API_BASE_URL}/feeding/records`);
    if (feedingResponse.ok) {
      const feedingData = await feedingResponse.json();
      const feedingRecords = feedingData.records || feedingData;
      result.feedingRecordsCount = Array.isArray(feedingRecords) ? feedingRecords.filter((f: any) =>
        f.animalName === 'Tshepiso' || f.animalName === 'Lerato'
      ).length : 0;
    }

    // Check financial records
    const financialResponse = await fetch(`${API_BASE_URL}/financial/records`);
    if (financialResponse.ok) {
      const financialData = await financialResponse.json();
      const financialRecords = financialData.records || financialData;
      result.financialRecordsCount = Array.isArray(financialRecords) ? financialRecords.filter((f: any) =>
        f.relatedAnimalName === 'Tshepiso' || f.relatedAnimalName === 'Lerato'
      ).length : 0;
    }

    result.relationshipsValid = result.betaAnimalsCount >= 2 && 
                               result.healthRecordsCount >= 2 && 
                               result.feedingRecordsCount >= 2 && 
                               result.financialRecordsCount >= 2;

  } catch (error) {
    console.error('Data integrity check failed:', error);
  }

  return result;
}

/**
 * Verify visual consistency
 */
function verifyVisualConsistency(): any {
  const result = {
    themeApplied: false,
    glassmorphismEffects: false,
    backgroundsVisible: false,
    noWhiteBackgrounds: true
  };

  try {
    // Check for AgriIntel theme colors
    const agriIntelColors = ['#1565C0', '#2E7D32', '#F57C00'];
    const elementsWithTheme = Array.from(document.querySelectorAll('*')).filter(el => {
      const styles = window.getComputedStyle(el);
      return agriIntelColors.some(color => 
        styles.color.includes(color) || 
        styles.backgroundColor.includes(color) || 
        styles.borderColor.includes(color)
      );
    });
    result.themeApplied = elementsWithTheme.length > 0;

    // Check for glassmorphism effects
    const glassmorphElements = document.querySelectorAll('[style*="backdrop-filter"], [style*="backdropFilter"]');
    result.glassmorphismEffects = glassmorphElements.length > 0;

    // Check for background images
    const backgroundElements = document.querySelectorAll('[style*="background-image"]');
    result.backgroundsVisible = backgroundElements.length > 0;

    // Check for problematic white backgrounds
    const whiteBackgrounds = document.querySelectorAll('[style*="background: #FFFFFF"], [style*="background-color: white"]');
    result.noWhiteBackgrounds = whiteBackgrounds.length === 0;

  } catch (error) {
    console.error('Visual consistency check failed:', error);
  }

  return result;
}

/**
 * Verify functional elements
 */
function verifyFunctionalElements(): any {
  const result = {
    formsWorking: false,
    buttonsClickable: false,
    navigationSmooth: false,
    crudOperations: false
  };

  try {
    // Check forms
    const forms = document.querySelectorAll('form, .MuiTextField-root, input, select');
    result.formsWorking = forms.length > 0;

    // Check buttons
    const workingButtons = document.querySelectorAll('button:not([disabled])');
    result.buttonsClickable = workingButtons.length > 0;

    // Check navigation
    const navElements = document.querySelectorAll('nav, .navigation, .sidebar, [role="navigation"]');
    result.navigationSmooth = navElements.length > 0;

    // Check for CRUD operation elements
    const crudElements = document.querySelectorAll('[data-action="add"], [data-action="edit"], [data-action="delete"], .add-button, .edit-button, .delete-button');
    result.crudOperations = crudElements.length > 0;

  } catch (error) {
    console.error('Functional elements check failed:', error);
  }

  return result;
}

/**
 * Verify BETA compliance
 */
function verifyBetaCompliance(): any {
  const result = {
    limitEnforced: false,
    upgradePrompts: false,
    restrictedModulesBlocked: false
  };

  try {
    // Check for limit enforcement
    const limitElements = document.querySelectorAll('[data-limit], .limit-indicator, .usage-progress');
    result.limitEnforced = limitElements.length > 0;

    // Check for upgrade prompts
    const upgradeElements = document.querySelectorAll('[data-upgrade], .upgrade-prompt, .upgrade-button');
    result.upgradePrompts = upgradeElements.length > 0;

    // Check for restricted modules
    const restrictedElements = document.querySelectorAll('[data-restricted], .locked-module, .premium-only');
    result.restrictedModulesBlocked = restrictedElements.length > 0;

  } catch (error) {
    console.error('BETA compliance check failed:', error);
  }

  return result;
}

/**
 * Run comprehensive LIVE deployment verification
 */
export async function runLiveDeploymentVerification(): Promise<LiveDeploymentCheck> {
  console.log('🚀 Starting LIVE Deployment Verification...');

  const apiEndpoints = await verifyApiEndpoints();
  const dataIntegrity = await verifyDataIntegrity();
  const visualConsistency = verifyVisualConsistency();
  const functionalElements = verifyFunctionalElements();
  const betaCompliance = verifyBetaCompliance();

  const issues: string[] = [];

  // Collect issues
  if (!apiEndpoints.allWorking) issues.push('Some API endpoints not working');
  if (!dataIntegrity.relationshipsValid) issues.push('Data relationships not valid');
  if (!visualConsistency.themeApplied) issues.push('AgriIntel theme not applied');
  if (!visualConsistency.glassmorphismEffects) issues.push('Glassmorphism effects missing');
  if (!visualConsistency.noWhiteBackgrounds) issues.push('White backgrounds covering design');
  if (!functionalElements.formsWorking) issues.push('Forms not working');
  if (!functionalElements.buttonsClickable) issues.push('Buttons not clickable');
  if (!betaCompliance.limitEnforced) issues.push('BETA limits not enforced');

  const readyForLive = issues.length === 0;

  const result: LiveDeploymentCheck = {
    apiEndpoints,
    dataIntegrity,
    visualConsistency,
    functionalElements,
    betaCompliance,
    readyForLive,
    issues
  };

  console.log('✅ LIVE Deployment Verification Complete');
  console.log(`Ready for LIVE: ${readyForLive ? 'YES' : 'NO'}`);
  if (issues.length > 0) {
    console.log('Issues found:', issues);
  }

  return result;
}

// Export for global access
(window as any).liveDeploymentVerification = {
  runLiveDeploymentVerification
};
