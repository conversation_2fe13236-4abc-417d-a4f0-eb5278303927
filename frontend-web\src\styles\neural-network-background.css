/*
 * NEURAL NETWORK BACKGROUND STYLES
 * Animated particle system for futuristic design
 */

.neural-network-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.neural-network-canvas {
  width: 100%;
  height: 100%;
  display: block;
}

/* Performance optimizations for animations */
.neural-network-container canvas {
  will-change: transform;
  transform: translateZ(0);
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .neural-network-container {
    display: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .neural-network-container {
    opacity: 0.8;
  }
}
