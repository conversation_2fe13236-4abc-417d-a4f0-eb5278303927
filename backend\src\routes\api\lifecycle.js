/**
 * Lifecycle Management API Routes
 * 
 * This module provides API routes for automated lifecycle management.
 */

const express = require('express');
const router = express.Router();
const lifecycleManagementService = require('../../services/lifecycleManagementService');
const lifecycleSchedulerService = require('../../services/lifecycleSchedulerService');
const { authenticate, authorize, requirePermission } = require('../../middleware/authMiddleware');
const logger = require('../../utils/logger');

/**
 * @route GET /api/lifecycle/dashboard
 * @desc Get lifecycle management dashboard data
 * @access Private
 */
router.get('/dashboard', authenticate, async (req, res, next) => {
  try {
    const dashboardData = await lifecycleSchedulerService.getDashboardData();
    
    res.json({
      success: true,
      data: dashboardData
    });
  } catch (error) {
    logger.error('Error getting lifecycle dashboard data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get lifecycle dashboard data',
      error: error.message
    });
  }
});

/**
 * @route GET /api/lifecycle/statistics
 * @desc Get lifecycle statistics
 * @access Private
 */
router.get('/statistics', authenticate, async (req, res, next) => {
  try {
    const statistics = await lifecycleManagementService.getLifecycleStatistics();
    
    res.json({
      success: true,
      data: statistics
    });
  } catch (error) {
    logger.error('Error getting lifecycle statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get lifecycle statistics',
      error: error.message
    });
  }
});

/**
 * @route GET /api/lifecycle/stages/:stage
 * @desc Get animals by lifecycle stage
 * @access Private
 */
router.get('/stages/:stage', authenticate, async (req, res, next) => {
  try {
    const { stage } = req.params;
    const validStages = ['calf', 'juvenile', 'adult', 'breeding', 'retirement'];
    
    if (!validStages.includes(stage)) {
      return res.status(400).json({
        success: false,
        message: `Invalid lifecycle stage. Valid stages: ${validStages.join(', ')}`
      });
    }
    
    const animals = await lifecycleManagementService.getAnimalsByLifecycleStage(stage);
    
    res.json({
      success: true,
      data: {
        stage,
        count: animals.length,
        animals
      }
    });
  } catch (error) {
    logger.error(`Error getting animals by lifecycle stage ${req.params.stage}:`, error);
    res.status(500).json({
      success: false,
      message: 'Failed to get animals by lifecycle stage',
      error: error.message
    });
  }
});

/**
 * @route GET /api/lifecycle/transfer-candidates
 * @desc Get animals that are candidates for inventory transfer
 * @access Private
 */
router.get('/transfer-candidates', authenticate, async (req, res, next) => {
  try {
    const { priority, reason } = req.query;
    
    let candidates = await lifecycleManagementService.getTransferCandidates();
    
    // Filter by priority if specified
    if (priority) {
      candidates = candidates.filter(candidate => candidate.priority === priority);
    }
    
    // Filter by reason if specified
    if (reason) {
      candidates = candidates.filter(candidate => candidate.reason === reason);
    }
    
    res.json({
      success: true,
      data: {
        total: candidates.length,
        filters: { priority, reason },
        candidates
      }
    });
  } catch (error) {
    logger.error('Error getting transfer candidates:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get transfer candidates',
      error: error.message
    });
  }
});

/**
 * @route POST /api/lifecycle/update-stages
 * @desc Manually trigger lifecycle stage updates for all animals
 * @access Private (Admin/Manager only)
 */
router.post('/update-stages', authenticate, authorize(['admin', 'manager']), async (req, res, next) => {
  try {
    const result = await lifecycleManagementService.updateAllLifecycleStages();
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('Error updating lifecycle stages:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update lifecycle stages',
      error: error.message
    });
  }
});

/**
 * @route POST /api/lifecycle/transfer/:animalId
 * @desc Execute inventory transfer for a specific animal
 * @access Private (Admin/Manager only)
 */
router.post('/transfer/:animalId', authenticate, authorize(['admin', 'manager']), async (req, res, next) => {
  try {
    const { animalId } = req.params;
    const { reason, notes } = req.body;
    
    if (!reason) {
      return res.status(400).json({
        success: false,
        message: 'Transfer reason is required'
      });
    }
    
    const result = await lifecycleManagementService.executeInventoryTransfer(animalId, reason, notes);
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error(`Error executing inventory transfer for animal ${req.params.animalId}:`, error);
    res.status(500).json({
      success: false,
      message: 'Failed to execute inventory transfer',
      error: error.message
    });
  }
});

/**
 * @route GET /api/lifecycle/scheduler/status
 * @desc Get lifecycle scheduler status
 * @access Private (Admin only)
 */
router.get('/scheduler/status', authenticate, authorize(['admin']), async (req, res, next) => {
  try {
    const status = lifecycleSchedulerService.getSchedulerStatus();
    
    res.json({
      success: true,
      data: status
    });
  } catch (error) {
    logger.error('Error getting scheduler status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get scheduler status',
      error: error.message
    });
  }
});

/**
 * @route POST /api/lifecycle/scheduler/trigger
 * @desc Manually trigger lifecycle update
 * @access Private (Admin only)
 */
router.post('/scheduler/trigger', authenticate, authorize(['admin']), async (req, res, next) => {
  try {
    const result = await lifecycleSchedulerService.triggerManualUpdate();
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('Error triggering manual update:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to trigger manual update',
      error: error.message
    });
  }
});

/**
 * @route POST /api/lifecycle/scheduler/restart
 * @desc Restart lifecycle scheduler
 * @access Private (Admin only)
 */
router.post('/scheduler/restart', authenticate, authorize(['admin']), async (req, res, next) => {
  try {
    lifecycleSchedulerService.restartAllTasks();
    
    res.json({
      success: true,
      message: 'Lifecycle scheduler restarted successfully'
    });
  } catch (error) {
    logger.error('Error restarting scheduler:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to restart scheduler',
      error: error.message
    });
  }
});

/**
 * @route GET /api/lifecycle/animal/:animalId/stage-history
 * @desc Get lifecycle stage history for a specific animal
 * @access Private
 */
router.get('/animal/:animalId/stage-history', authenticate, async (req, res, next) => {
  try {
    const { animalId } = req.params;
    
    const Animal = require('../../models/schemas/animal.schema');
    const animal = await Animal.findById(animalId).select('tagNumber name lifecycle birthDate');
    
    if (!animal) {
      return res.status(404).json({
        success: false,
        message: 'Animal not found'
      });
    }
    
    const ageInMonths = lifecycleManagementService.calculateAgeInMonths(animal.birthDate);
    const expectedStage = lifecycleManagementService.getExpectedLifecycleStage(ageInMonths, animal.species);
    const transferStatus = lifecycleManagementService.shouldTransferToInventory(animal);
    
    res.json({
      success: true,
      data: {
        animal: {
          id: animal._id,
          tagNumber: animal.tagNumber,
          name: animal.name,
          ageInMonths,
          expectedStage,
          currentStage: animal.lifecycle?.currentStage,
          transferStatus
        },
        stageHistory: animal.lifecycle?.stageHistory || [],
        transferHistory: animal.lifecycle?.transferHistory || []
      }
    });
  } catch (error) {
    logger.error(`Error getting stage history for animal ${req.params.animalId}:`, error);
    res.status(500).json({
      success: false,
      message: 'Failed to get animal stage history',
      error: error.message
    });
  }
});

module.exports = router;
