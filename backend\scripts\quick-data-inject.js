const { MongoClient, ObjectId } = require('mongodb');
require('dotenv').config();

// MongoDB connection
const uri = process.env.MONGODB_URI || "mongodb+srv://AMPD:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0";
const dbName = process.env.MONGODB_DB_NAME || 'ampd_livestock';

console.log('Using MongoDB URI:', uri.replace(/:[^:]*@/, ':****@'));
console.log('Using Database:', dbName);

// Sample data for all modules
const sampleData = {
  animals: [
    {
      _id: new ObjectId(),
      tagNumber: 'CTL-2025-001',
      name: 'Thabo',
      species: 'Cattle',
      breed: 'Nguni',
      gender: 'Male',
      birthDate: new Date('2022-03-15'),
      weight: 450,
      status: 'Active',
      healthStatus: 'Healthy',
      location: 'North Paddock',
      purchasePrice: 15000,
      estimatedValue: 25000,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: new ObjectId(),
      tagNumber: 'CTL-2025-002',
      name: 'Nomsa',
      species: 'Cattle',
      breed: 'Brahman',
      gender: 'Female',
      birthDate: new Date('2021-08-20'),
      weight: 380,
      status: 'Active',
      healthStatus: 'Healthy',
      location: 'South Paddock',
      purchasePrice: 12000,
      estimatedValue: 20000,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: new ObjectId(),
      tagNumber: 'SHP-2025-001',
      name: 'Sipho',
      species: 'Sheep',
      breed: 'Dorper',
      gender: 'Male',
      birthDate: new Date('2023-01-10'),
      weight: 65,
      status: 'Active',
      healthStatus: 'Healthy',
      location: 'East Paddock',
      purchasePrice: 2500,
      estimatedValue: 4000,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ],
  
  health_records: [
    {
      _id: new ObjectId(),
      animalId: 'CTL-2025-001',
      recordType: 'Vaccination',
      date: new Date('2024-12-01'),
      veterinarian: 'Dr. Sipho Mkhize',
      diagnosis: 'Routine vaccination - FMD',
      treatment: 'Foot and Mouth Disease vaccine',
      cost: 150,
      notes: 'Annual vaccination completed successfully',
      status: 'Completed',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: new ObjectId(),
      animalId: 'CTL-2025-002',
      recordType: 'Treatment',
      date: new Date('2024-11-15'),
      veterinarian: 'Dr. Thandi Nkomo',
      diagnosis: 'Minor wound treatment',
      treatment: 'Antiseptic cleaning and bandaging',
      cost: 200,
      notes: 'Small cut on leg, healing well',
      status: 'Completed',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: new ObjectId(),
      animalId: 'SHP-2025-001',
      recordType: 'Examination',
      date: new Date('2024-12-10'),
      veterinarian: 'Dr. Sipho Mkhize',
      diagnosis: 'Healthy - routine checkup',
      treatment: 'General health examination',
      cost: 100,
      notes: 'Excellent health condition',
      status: 'Completed',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ],
  
  feeding_records: [
    {
      _id: new ObjectId(),
      animalId: 'CTL-2025-001',
      feedType: 'Lucerne Hay',
      quantity: 15,
      unit: 'kg',
      feedingTime: new Date('2024-12-15T06:00:00Z'),
      cost: 45,
      notes: 'Morning feeding - high quality lucerne',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: new ObjectId(),
      animalId: 'CTL-2025-002',
      feedType: 'Cattle Pellets',
      quantity: 8,
      unit: 'kg',
      feedingTime: new Date('2024-12-15T06:30:00Z'),
      cost: 32,
      notes: 'Morning supplement feeding',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: new ObjectId(),
      animalId: 'SHP-2025-001',
      feedType: 'Sheep Pellets',
      quantity: 2,
      unit: 'kg',
      feedingTime: new Date('2024-12-15T07:00:00Z'),
      cost: 12,
      notes: 'Morning feeding - protein supplement',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ],
  
  financial_records: [
    {
      _id: new ObjectId(),
      type: 'Income',
      category: 'Livestock Sales',
      amount: 25000,
      description: 'Sale of bull - CTL-2024-003',
      date: new Date('2024-11-20'),
      reference: 'INV-2024-001',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: new ObjectId(),
      type: 'Expense',
      category: 'Feed',
      amount: 3500,
      description: 'Monthly feed purchase - December',
      date: new Date('2024-12-01'),
      reference: 'EXP-2024-045',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: new ObjectId(),
      type: 'Expense',
      category: 'Veterinary',
      amount: 1200,
      description: 'Vaccination program - December',
      date: new Date('2024-12-05'),
      reference: 'EXP-2024-046',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ]
};

async function injectData() {
  let client;
  
  try {
    console.log('Connecting to MongoDB...');
    client = new MongoClient(uri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    
    await client.connect();
    console.log('Connected successfully to MongoDB');
    
    const db = client.db(dbName);
    
    // Inject data for each collection
    for (const [collectionName, data] of Object.entries(sampleData)) {
      console.log(`\nInjecting data into ${collectionName}...`);
      
      const collection = db.collection(collectionName);
      
      // Clear existing data
      await collection.deleteMany({});
      console.log(`Cleared existing data from ${collectionName}`);
      
      // Insert new data
      const result = await collection.insertMany(data);
      console.log(`Inserted ${result.insertedCount} records into ${collectionName}`);
    }
    
    console.log('\n✅ Data injection completed successfully!');
    
    // Verify data
    console.log('\n📊 Verification:');
    for (const collectionName of Object.keys(sampleData)) {
      const collection = db.collection(collectionName);
      const count = await collection.countDocuments();
      console.log(`${collectionName}: ${count} records`);
    }
    
  } catch (error) {
    console.error('❌ Error injecting data:', error);
  } finally {
    if (client) {
      await client.close();
      console.log('\nMongoDB connection closed');
    }
  }
}

// Run the injection
injectData();
