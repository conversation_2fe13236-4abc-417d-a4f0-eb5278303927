/**
 * BETA API Endpoints Comprehensive Audit Script
 * Tests all BETA tier API endpoints and verifies data integration
 */

const axios = require('axios');
const logger = require('../src/utils/logger');

const API_BASE_URL = 'http://localhost:3002/api';
const BETA_USER_CREDENTIALS = {
  username: 'Demo',
  password: '123'
};

let authToken = null;

// Helper function to make authenticated API calls
async function apiCall(method, endpoint, data = null, headers = {}) {
  try {
    const config = {
      method,
      url: `${API_BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    if (authToken) {
      config.headers.Authorization = `Bearer ${authToken}`;
    }

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return {
      success: true,
      status: response.status,
      data: response.data
    };
  } catch (error) {
    return {
      success: false,
      status: error.response?.status || 500,
      error: error.response?.data || error.message
    };
  }
}

// Authenticate as BETA user
async function authenticateBetaUser() {
  console.log('🔐 Authenticating BETA user...');
  
  const result = await apiCall('POST', '/auth/login', BETA_USER_CREDENTIALS);
  
  if (result.success && result.data.token) {
    authToken = result.data.token;
    console.log('✅ BETA user authenticated successfully');
    return true;
  } else {
    console.log('❌ BETA user authentication failed:', result.error);
    return false;
  }
}

// Test Animals API endpoints
async function testAnimalsAPI() {
  console.log('\n🐄 Testing Animals API...');
  
  const tests = [
    {
      name: 'Get all animals',
      method: 'GET',
      endpoint: '/animals',
      expectedStatus: 200
    },
    {
      name: 'Get animal statistics',
      method: 'GET',
      endpoint: '/animals/statistics',
      expectedStatus: 200
    },
    {
      name: 'Get BETA animal by tag',
      method: 'GET',
      endpoint: '/animals/tag/BETA-001',
      expectedStatus: 200
    }
  ];

  const results = [];
  for (const test of tests) {
    const result = await apiCall(test.method, test.endpoint);
    const passed = result.status === test.expectedStatus;
    
    console.log(`${passed ? '✅' : '❌'} ${test.name}: ${result.status}`);
    
    if (passed && result.data) {
      if (test.endpoint === '/animals') {
        console.log(`   📊 Found ${result.data.animals?.length || 0} animals`);
      } else if (test.endpoint === '/animals/statistics') {
        console.log(`   📊 Stats: ${JSON.stringify(result.data.stats || {})}`);
      }
    }
    
    results.push({ ...test, passed, result });
  }
  
  return results;
}

// Test Health API endpoints
async function testHealthAPI() {
  console.log('\n🏥 Testing Health API...');
  
  const tests = [
    {
      name: 'Get all health records',
      method: 'GET',
      endpoint: '/health/records',
      expectedStatus: 200
    },
    {
      name: 'Get health statistics',
      method: 'GET',
      endpoint: '/health/stats',
      expectedStatus: 200
    }
  ];

  const results = [];
  for (const test of tests) {
    const result = await apiCall(test.method, test.endpoint);
    const passed = result.status === test.expectedStatus;
    
    console.log(`${passed ? '✅' : '❌'} ${test.name}: ${result.status}`);
    
    if (passed && result.data) {
      if (test.endpoint === '/health/records') {
        console.log(`   📊 Found ${result.data.records?.length || 0} health records`);
      }
    }
    
    results.push({ ...test, passed, result });
  }
  
  return results;
}

// Test Feeding API endpoints
async function testFeedingAPI() {
  console.log('\n🌾 Testing Feeding API...');
  
  const tests = [
    {
      name: 'Get feeding records',
      method: 'GET',
      endpoint: '/feeding/records',
      expectedStatus: 200
    },
    {
      name: 'Get feeding statistics',
      method: 'GET',
      endpoint: '/feeding/stats',
      expectedStatus: 200
    }
  ];

  const results = [];
  for (const test of tests) {
    const result = await apiCall(test.method, test.endpoint);
    const passed = result.status === test.expectedStatus;
    
    console.log(`${passed ? '✅' : '❌'} ${test.name}: ${result.status}`);
    
    if (passed && result.data) {
      if (test.endpoint === '/feeding/records') {
        console.log(`   📊 Found ${result.data.records?.length || 0} feeding records`);
      }
    }
    
    results.push({ ...test, passed, result });
  }
  
  return results;
}

// Test Financial API endpoints
async function testFinancialAPI() {
  console.log('\n💰 Testing Financial API...');
  
  const tests = [
    {
      name: 'Get financial records',
      method: 'GET',
      endpoint: '/financial/records',
      expectedStatus: 200
    },
    {
      name: 'Get financial statistics',
      method: 'GET',
      endpoint: '/financial/stats',
      expectedStatus: 200
    }
  ];

  const results = [];
  for (const test of tests) {
    const result = await apiCall(test.method, test.endpoint);
    const passed = result.status === test.expectedStatus;
    
    console.log(`${passed ? '✅' : '❌'} ${test.name}: ${result.status}`);
    
    if (passed && result.data) {
      if (test.endpoint === '/financial/records') {
        console.log(`   📊 Found ${result.data.records?.length || 0} financial records`);
      }
    }
    
    results.push({ ...test, passed, result });
  }
  
  return results;
}

// Test BETA tier compliance
async function testBetaCompliance() {
  console.log('\n🔒 Testing BETA Tier Compliance...');
  
  // Test animal limit enforcement
  const animalsResult = await apiCall('GET', '/animals');
  if (animalsResult.success) {
    const animalCount = animalsResult.data.animals?.length || 0;
    const withinLimit = animalCount <= 50;
    console.log(`${withinLimit ? '✅' : '❌'} Animal limit check: ${animalCount}/50 animals`);
  }
  
  // Test access to Professional-only endpoints
  const professionalTests = [
    { name: 'Breeding records (should be restricted)', endpoint: '/breeding/records' },
    { name: 'Inventory (should be restricted)', endpoint: '/inventory' },
    { name: 'Analytics (should be restricted)', endpoint: '/business/analytics' }
  ];
  
  for (const test of professionalTests) {
    const result = await apiCall('GET', test.endpoint);
    const isRestricted = result.status === 403 || result.status === 401;
    console.log(`${isRestricted ? '✅' : '❌'} ${test.name}: ${result.status}`);
  }
}

// Verify data relationships
async function verifyDataRelationships() {
  console.log('\n🔗 Verifying Data Relationships...');
  
  // Get animals and check if health/feeding records reference them
  const animalsResult = await apiCall('GET', '/animals');
  const healthResult = await apiCall('GET', '/health/records');
  const feedingResult = await apiCall('GET', '/feeding/records');
  
  if (animalsResult.success && healthResult.success && feedingResult.success) {
    const animals = animalsResult.data.animals || [];
    const healthRecords = healthResult.data.records || [];
    const feedingRecords = feedingResult.data.records || [];
    
    console.log(`📊 Data Summary:`);
    console.log(`   Animals: ${animals.length}`);
    console.log(`   Health Records: ${healthRecords.length}`);
    console.log(`   Feeding Records: ${feedingRecords.length}`);
    
    // Check relationships
    const animalIds = animals.map(a => a._id || a.id);
    const healthAnimalRefs = healthRecords.filter(h => animalIds.includes(h.animalId));
    const feedingAnimalRefs = feedingRecords.filter(f => animalIds.includes(f.animalId));
    
    console.log(`🔗 Relationship Verification:`);
    console.log(`   Health records linked to animals: ${healthAnimalRefs.length}/${healthRecords.length}`);
    console.log(`   Feeding records linked to animals: ${feedingAnimalRefs.length}/${feedingRecords.length}`);
  }
}

// Main audit function
async function runBetaAudit() {
  console.log('🚀 Starting BETA API Endpoints Comprehensive Audit\n');
  
  // Authenticate
  const authenticated = await authenticateBetaUser();
  if (!authenticated) {
    console.log('❌ Cannot proceed without authentication');
    return;
  }
  
  // Run all tests
  const animalsResults = await testAnimalsAPI();
  const healthResults = await testHealthAPI();
  const feedingResults = await testFeedingAPI();
  const financialResults = await testFinancialAPI();
  
  await testBetaCompliance();
  await verifyDataRelationships();
  
  // Summary
  const allResults = [...animalsResults, ...healthResults, ...feedingResults, ...financialResults];
  const passedTests = allResults.filter(r => r.passed).length;
  const totalTests = allResults.length;
  
  console.log('\n📋 AUDIT SUMMARY');
  console.log('================');
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${passedTests}`);
  console.log(`Failed: ${totalTests - passedTests}`);
  console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  if (passedTests === totalTests) {
    console.log('\n✅ All BETA API endpoints are functioning correctly!');
  } else {
    console.log('\n❌ Some BETA API endpoints need attention.');
  }
}

// Run the audit
if (require.main === module) {
  runBetaAudit()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('❌ Audit failed:', error);
      process.exit(1);
    });
}

module.exports = { runBetaAudit };
