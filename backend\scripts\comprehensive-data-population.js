/**
 * Comprehensive Data Population Script
 * 
 * Creates 2 realistic, detailed records for EACH module and sub-module
 * with proper interconnections and relationships.
 */

const { MongoClient, ObjectId } = require('mongodb');
require('dotenv').config();

// MongoDB connection
const uri = process.env.MONGODB_URI || "mongodb+srv://AMPD:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0";
const dbName = process.env.MONGODB_DB_NAME || 'ampd_livestock';

console.log('🚀 Starting Comprehensive Data Population...');
console.log(`Database: ${dbName}`);

// Generate realistic South African names
const southAfricanNames = {
  male: ['Thabo', 'Sipho', 'Mandla', 'Bongani', 'Tshepo', 'Kagiso', 'Lerato', '<PERSON><PERSON><PERSON>'],
  female: ['No<PERSON>a', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>ledi', '<PERSON>bogo']
};

const breeds = {
  cattle: ['<PERSON>uni', '<PERSON>rahman', 'Angus', 'Hereford', 'Simmental', 'Charolais', 'Limousin', 'Bonsmara'],
  sheep: ['Dorper', 'Merino', 'Blackhead Persian', 'Damara', 'Van Rooy', 'Dohne Merino'],
  goat: ['Boer', 'Kalahari Red', 'Savanna', 'Angora', 'Indigenous Goat'],
  pig: ['Large White', 'Landrace', 'Duroc', 'Hampshire', 'Pietrain']
};

// Helper function to generate random date within range
function randomDate(start, end) {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
}

// Helper function to generate tag number
function generateTagNumber(species, year, sequence) {
  const speciesCodes = {
    cattle: 'CTL',
    sheep: 'SHP',
    goat: 'GOT',
    pig: 'PIG'
  };
  return `${speciesCodes[species] || 'ANM'}-${year}-${sequence.toString().padStart(3, '0')}`;
}

// Create comprehensive animal data
function createAnimalData() {
  const animals = [];
  const species = ['cattle', 'sheep', 'goat'];
  let animalId = 1;

  species.forEach(speciesType => {
    for (let i = 1; i <= 2; i++) {
      const gender = i === 1 ? 'male' : 'female';
      const name = southAfricanNames[gender][Math.floor(Math.random() * southAfricanNames[gender].length)];
      const breed = breeds[speciesType][Math.floor(Math.random() * breeds[speciesType].length)];
      const birthDate = randomDate(new Date(2020, 0, 1), new Date(2023, 11, 31));
      const ageInMonths = Math.floor((new Date() - birthDate) / (1000 * 60 * 60 * 24 * 30.44));
      
      // Determine lifecycle stage based on age
      let lifecycleStage = 'adult';
      if (ageInMonths < 6) lifecycleStage = 'calf';
      else if (ageInMonths < 12) lifecycleStage = 'juvenile';
      else if (ageInMonths < 96) lifecycleStage = 'adult';
      else lifecycleStage = 'retirement';

      const animal = {
        _id: new ObjectId(),
        tagNumber: generateTagNumber(speciesType, birthDate.getFullYear(), animalId),
        name: name,
        species: speciesType,
        breed: breed,
        gender: gender,
        birthDate: birthDate,
        weight: speciesType === 'cattle' ? 350 + Math.random() * 200 : 
                speciesType === 'sheep' ? 40 + Math.random() * 30 :
                30 + Math.random() * 25,
        status: 'active',
        healthStatus: Math.random() > 0.1 ? 'healthy' : 'sick',
        location: ['North Paddock', 'South Paddock', 'East Paddock', 'West Paddock'][Math.floor(Math.random() * 4)],
        
        // Enhanced identification
        identification: {
          rfidTag: `RF${Math.floor(Math.random() * **********000).toString().padStart(12, '0')}`,
          microchipId: Math.random() > 0.5 ? `MC${Math.floor(Math.random() * **********000000).toString().padStart(15, '0')}` : undefined,
          earTagLeft: `L${animalId.toString().padStart(3, '0')}`,
          earTagRight: `R${animalId.toString().padStart(3, '0')}`,
          registrationNumber: `REG-${birthDate.getFullYear()}-${animalId.toString().padStart(4, '0')}`,
          nationalId: `ZA${Math.floor(Math.random() * **********).toString().padStart(9, '0')}`
        },

        // Physical characteristics
        physicalCharacteristics: {
          height: { value: speciesType === 'cattle' ? 120 + Math.random() * 40 : 60 + Math.random() * 20, unit: 'cm' },
          length: { value: speciesType === 'cattle' ? 150 + Math.random() * 50 : 80 + Math.random() * 30, unit: 'cm' },
          chestGirth: { value: speciesType === 'cattle' ? 160 + Math.random() * 40 : 90 + Math.random() * 20, unit: 'cm' },
          colorMarkings: {
            primaryColor: ['Black', 'Brown', 'White', 'Red', 'Grey'][Math.floor(Math.random() * 5)],
            secondaryColor: Math.random() > 0.5 ? ['White', 'Black', 'Brown'][Math.floor(Math.random() * 3)] : undefined,
            markings: ['White face', 'White legs', 'Spotted pattern'][Math.floor(Math.random() * 3)],
            distinguishingFeatures: [`Distinctive ${['scar', 'marking', 'coloration'][Math.floor(Math.random() * 3)]} on ${['left side', 'right side', 'face'][Math.floor(Math.random() * 3)]}`]
          },
          bodyConditionScore: 3 + Math.floor(Math.random() * 5) // 3-7 range
        },

        // Genealogy (simplified for this example)
        genealogy: {
          sire: i > 1 ? {
            tagNumber: generateTagNumber(speciesType, birthDate.getFullYear() - 2, 1),
            name: southAfricanNames.male[0],
            breed: breed,
            lineage: `${breed} Champion Line`
          } : undefined,
          dam: {
            tagNumber: generateTagNumber(speciesType, birthDate.getFullYear() - 3, 2),
            name: southAfricanNames.female[0],
            breed: breed,
            lineage: `${breed} Matriarch Line`
          },
          geneticTraits: {
            bloodline: `${breed} Premium`,
            geneticMarkers: ['A2A2', 'BB', 'Polled'],
            heritableTraits: ['Fast growth', 'Disease resistance', 'Good temperament'],
            breedingValue: 85 + Math.random() * 15
          }
        },

        // Lifecycle management
        lifecycle: {
          currentStage: lifecycleStage,
          stageHistory: [{
            stage: 'calf',
            startDate: birthDate,
            endDate: ageInMonths > 6 ? new Date(birthDate.getTime() + 6 * 30.44 * 24 * 60 * 60 * 1000) : null,
            autoTransitioned: true,
            notes: 'Initial stage at birth'
          }],
          expectedRetirementDate: new Date(birthDate.getTime() + 10 * 365.25 * 24 * 60 * 60 * 1000)
        },

        // Acquisition details
        acquisition: {
          purchasePrice: 8000 + Math.random() * 12000,
          purchaseDate: randomDate(birthDate, new Date()),
          vendor: {
            name: ['Mthembu Farms', 'Botha Livestock', 'Ndlovu Ranch'][Math.floor(Math.random() * 3)],
            contact: '+27 82 ' + Math.floor(Math.random() * 1000000).toString().padStart(7, '0'),
            address: ['Limpopo Province', 'Mpumalanga Province', 'KwaZulu-Natal Province'][Math.floor(Math.random() * 3)]
          },
          healthCertificates: [{
            type: 'Health Certificate',
            issueDate: randomDate(new Date(2024, 0, 1), new Date()),
            expiryDate: new Date(2025, 11, 31),
            issuingAuthority: 'Department of Agriculture'
          }]
        },

        // Financial tracking
        financial: {
          estimatedValue: 12000 + Math.random() * 8000,
          maintenanceCosts: [{
            date: randomDate(new Date(2024, 0, 1), new Date()),
            category: 'Feed',
            amount: 500 + Math.random() * 1000,
            description: 'Monthly feed costs'
          }],
          revenueGenerated: gender === 'female' && Math.random() > 0.5 ? [{
            date: randomDate(new Date(2024, 0, 1), new Date()),
            source: 'Breeding',
            amount: 2000 + Math.random() * 3000,
            description: 'Breeding fee'
          }] : []
        },

        createdAt: new Date(),
        updatedAt: new Date()
      };

      animals.push(animal);
      animalId++;
    }
  });

  return animals;
}

// Create health records data
function createHealthRecords(animals) {
  const healthRecords = [];
  const recordTypes = ['vaccination', 'treatment', 'examination', 'surgery'];
  const veterinarians = [
    { name: 'Dr. Sipho Mkhize', contact: '+27 82 123 4567', clinic: 'Farmland Veterinary Clinic' },
    { name: 'Dr. Thandi Nkomo', contact: '+27 83 234 5678', clinic: 'Rural Animal Health Services' }
  ];

  animals.forEach((animal, index) => {
    // Create 2 health records per animal
    for (let i = 0; i < 2; i++) {
      const recordType = recordTypes[Math.floor(Math.random() * recordTypes.length)];
      const vet = veterinarians[Math.floor(Math.random() * veterinarians.length)];
      const recordDate = randomDate(new Date(2024, 0, 1), new Date());

      const healthRecord = {
        _id: new ObjectId(),
        animalId: animal._id,
        animalTagNumber: animal.tagNumber,
        recordType: recordType,
        date: recordDate,
        veterinarian: vet,
        diagnosis: recordType === 'vaccination' ? 'Routine vaccination - FMD' :
                  recordType === 'treatment' ? 'Minor wound treatment' :
                  recordType === 'examination' ? 'Annual health examination' :
                  'Minor surgical procedure',
        treatment: recordType === 'vaccination' ? 'Foot and Mouth Disease vaccine' :
                  recordType === 'treatment' ? 'Antiseptic cleaning and bandaging' :
                  recordType === 'examination' ? 'Complete physical examination' :
                  'Surgical intervention',
        medications: recordType === 'treatment' ? [{
          name: 'Antibiotic cream',
          dosage: '5ml',
          frequency: 'Twice daily',
          duration: '7 days'
        }] : [],
        cost: 150 + Math.random() * 500,
        notes: `${recordType} completed successfully. Animal responded well to treatment.`,
        status: 'completed',
        followUp: {
          required: recordType === 'surgery',
          date: recordType === 'surgery' ? new Date(recordDate.getTime() + 14 * 24 * 60 * 60 * 1000) : null,
          notes: recordType === 'surgery' ? 'Check surgical site in 2 weeks' : null
        },
        createdAt: new Date(),
        updatedAt: new Date()
      };

      healthRecords.push(healthRecord);
    }
  });

  return healthRecords;
}

// Create feeding records data
function createFeedingRecords(animals) {
  const feedingRecords = [];
  const feedTypes = [
    { name: 'Lucerne Hay', costPerKg: 3.50, nutritionValue: 'High protein' },
    { name: 'Cattle Pellets', costPerKg: 4.20, nutritionValue: 'Balanced nutrition' },
    { name: 'Maize Silage', costPerKg: 2.80, nutritionValue: 'High energy' },
    { name: 'Sheep Pellets', costPerKg: 4.50, nutritionValue: 'Protein supplement' }
  ];

  animals.forEach((animal, index) => {
    // Create 2 feeding records per animal
    for (let i = 0; i < 2; i++) {
      const feedType = feedTypes[Math.floor(Math.random() * feedTypes.length)];
      const quantity = animal.species === 'cattle' ? 10 + Math.random() * 10 : 2 + Math.random() * 3;
      const feedingDate = randomDate(new Date(2024, 11, 1), new Date());

      const feedingRecord = {
        _id: new ObjectId(),
        animalId: animal._id,
        animalTagNumber: animal.tagNumber,
        feedType: feedType.name,
        quantity: Math.round(quantity * 10) / 10,
        unit: 'kg',
        feedingTime: feedingDate,
        cost: Math.round(quantity * feedType.costPerKg * 100) / 100,
        nutritionalInfo: {
          protein: 12 + Math.random() * 8,
          energy: 8 + Math.random() * 4,
          fiber: 15 + Math.random() * 10
        },
        feedingMethod: ['Manual', 'Automatic feeder', 'Pasture grazing'][Math.floor(Math.random() * 3)],
        notes: `${feedType.nutritionValue} feed provided. Animal consumed well.`,
        weather: ['Sunny', 'Cloudy', 'Rainy'][Math.floor(Math.random() * 3)],
        createdBy: 'Farm Worker',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      feedingRecords.push(feedingRecord);
    }
  });

  return feedingRecords;
}

// Create financial records data
function createFinancialRecords(animals) {
  const financialRecords = [];
  const incomeCategories = ['Livestock Sales', 'Breeding Fees', 'Milk Sales', 'Wool Sales'];
  const expenseCategories = ['Feed', 'Veterinary', 'Equipment', 'Labor', 'Utilities', 'Insurance'];

  // Create income records
  for (let i = 0; i < 4; i++) {
    const category = incomeCategories[i % incomeCategories.length];
    const amount = category === 'Livestock Sales' ? 15000 + Math.random() * 20000 :
                  category === 'Breeding Fees' ? 2000 + Math.random() * 3000 :
                  1000 + Math.random() * 2000;

    const incomeRecord = {
      _id: new ObjectId(),
      type: 'income',
      category: category,
      amount: Math.round(amount * 100) / 100,
      description: category === 'Livestock Sales' ? `Sale of ${animals[i % animals.length].species} - ${animals[i % animals.length].tagNumber}` :
                  category === 'Breeding Fees' ? `Breeding service fee for ${animals[i % animals.length].tagNumber}` :
                  `${category} revenue`,
      date: randomDate(new Date(2024, 0, 1), new Date()),
      reference: `INV-2024-${(i + 1).toString().padStart(3, '0')}`,
      paymentMethod: ['Bank Transfer', 'Cash', 'Cheque'][Math.floor(Math.random() * 3)],
      taxRate: 15, // VAT rate in South Africa
      taxAmount: Math.round(amount * 0.15 * 100) / 100,
      relatedAnimalId: animals[i % animals.length]._id,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    financialRecords.push(incomeRecord);
  }

  // Create expense records
  for (let i = 0; i < 6; i++) {
    const category = expenseCategories[i % expenseCategories.length];
    const amount = category === 'Feed' ? 3000 + Math.random() * 2000 :
                  category === 'Veterinary' ? 800 + Math.random() * 1200 :
                  category === 'Equipment' ? 5000 + Math.random() * 10000 :
                  500 + Math.random() * 1500;

    const expenseRecord = {
      _id: new ObjectId(),
      type: 'expense',
      category: category,
      amount: Math.round(amount * 100) / 100,
      description: category === 'Feed' ? 'Monthly feed purchase - December' :
                  category === 'Veterinary' ? 'Vaccination program and health checks' :
                  category === 'Equipment' ? 'New feeding equipment purchase' :
                  `${category} expenses`,
      date: randomDate(new Date(2024, 0, 1), new Date()),
      reference: `EXP-2024-${(i + 1).toString().padStart(3, '0')}`,
      vendor: category === 'Feed' ? 'AgriSupply Co.' :
              category === 'Veterinary' ? 'Farmland Veterinary Clinic' :
              category === 'Equipment' ? 'Farm Equipment Solutions' :
              'Various Suppliers',
      paymentMethod: ['Bank Transfer', 'Cash', 'Credit Card'][Math.floor(Math.random() * 3)],
      taxDeductible: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    financialRecords.push(expenseRecord);
  }

  return financialRecords;
}

// Main population function
async function populateComprehensiveData() {
  let client;
  
  try {
    console.log('🔗 Connecting to MongoDB...');
    client = new MongoClient(uri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    
    await client.connect();
    console.log('✅ Connected successfully to MongoDB');
    
    const db = client.db(dbName);
    
    // Create comprehensive data
    console.log('📊 Creating comprehensive data...');
    const animals = createAnimalData();
    const healthRecords = createHealthRecords(animals);
    const feedingRecords = createFeedingRecords(animals);
    const financialRecords = createFinancialRecords(animals);
    
    console.log(`Created ${animals.length} animals`);
    console.log(`Created ${healthRecords.length} health records`);
    console.log(`Created ${feedingRecords.length} feeding records`);
    console.log(`Created ${financialRecords.length} financial records`);
    
    // Clear existing data and insert new data
    console.log('🗑️ Clearing existing data...');
    await db.collection('animals').deleteMany({});
    await db.collection('health_records').deleteMany({});
    await db.collection('feeding_records').deleteMany({});
    await db.collection('financial_records').deleteMany({});
    
    console.log('💾 Inserting new comprehensive data...');
    
    // Insert animals
    const animalResult = await db.collection('animals').insertMany(animals);
    console.log(`✅ Inserted ${animalResult.insertedCount} animals`);
    
    // Insert health records
    const healthResult = await db.collection('health_records').insertMany(healthRecords);
    console.log(`✅ Inserted ${healthResult.insertedCount} health records`);
    
    // Insert feeding records
    const feedingResult = await db.collection('feeding_records').insertMany(feedingRecords);
    console.log(`✅ Inserted ${feedingResult.insertedCount} feeding records`);
    
    // Insert financial records
    const financialResult = await db.collection('financial_records').insertMany(financialRecords);
    console.log(`✅ Inserted ${financialResult.insertedCount} financial records`);
    
    console.log('\n🎉 COMPREHENSIVE DATA POPULATION COMPLETED!');
    console.log('=====================================');
    console.log(`📊 Summary:`);
    console.log(`   Animals: ${animalResult.insertedCount} records`);
    console.log(`   Health Records: ${healthResult.insertedCount} records`);
    console.log(`   Feeding Records: ${feedingResult.insertedCount} records`);
    console.log(`   Financial Records: ${financialResult.insertedCount} records`);
    console.log(`   Total Records: ${animalResult.insertedCount + healthResult.insertedCount + feedingResult.insertedCount + financialResult.insertedCount}`);
    
  } catch (error) {
    console.error('❌ Error populating comprehensive data:', error);
  } finally {
    if (client) {
      await client.close();
      console.log('\n🔌 MongoDB connection closed');
    }
  }
}

// Run the population
populateComprehensiveData();
