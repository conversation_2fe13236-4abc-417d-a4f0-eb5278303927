@echo off
echo ========================================
echo  AGRIINTEL PRODUCTION STARTUP SCRIPT
echo  Professional Livestock Management Platform
echo ========================================
echo.

:: Set environment variables
set REACT_APP_API_URL=http://localhost:3001/api
set REACT_APP_DEMO_MODE=true
set NODE_ENV=development
set PORT=3002

echo [INFO] Environment configured:
echo   - Backend API: http://localhost:3001
echo   - Frontend: http://localhost:3002
echo   - Demo Mode: Enabled
echo.

:: Kill any existing processes on our ports
echo [STEP 1] Cleaning up existing processes...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":3001"') do (
    echo Killing process on port 3001: %%a
    taskkill /f /pid %%a >nul 2>&1
)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":3002"') do (
    echo Killing process on port 3002: %%a
    taskkill /f /pid %%a >nul 2>&1
)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":5000"') do (
    echo Killing process on port 5000: %%a
    taskkill /f /pid %%a >nul 2>&1
)

:: Wait for ports to be freed
timeout /t 3 /nobreak >nul
echo [SUCCESS] Ports cleaned up
echo.

:: Check if Node.js is installed
echo [STEP 2] Checking Node.js installation...
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)
echo [SUCCESS] Node.js is available
echo.

:: Navigate to backend directory and start simple server
echo [STEP 3] Starting AgriIntel Backend Server...
cd /d "%~dp0backend"
if not exist "node_modules" (
    echo [INFO] Installing backend dependencies...
    call npm install
    if errorlevel 1 (
        echo [ERROR] Failed to install backend dependencies
        pause
        exit /b 1
    )
)

echo [INFO] Starting backend on port 3001...
start "AgriIntel Backend" cmd /k "echo AgriIntel Backend Server && echo Port: 3001 && echo API: http://localhost:3001/api && echo. && set PORT=3001 && node simple-server.js"

:: Wait for backend to start
timeout /t 5 /nobreak >nul

:: Test backend connectivity
echo [STEP 4] Testing backend connectivity...
curl -s http://localhost:3001/health >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Backend may still be starting up...
    timeout /t 3 /nobreak >nul
)

:: Navigate to frontend directory and start React app
echo [STEP 5] Starting AgriIntel Frontend...
cd /d "%~dp0frontend-web"
if not exist "node_modules" (
    echo [INFO] Installing frontend dependencies...
    call npm install
    if errorlevel 1 (
        echo [ERROR] Failed to install frontend dependencies
        pause
        exit /b 1
    )
)

echo [INFO] Starting frontend on port 3002...
set BROWSER=none
set PORT=3002
start "AgriIntel Frontend" cmd /k "echo AgriIntel Frontend Application && echo Port: 3002 && echo URL: http://localhost:3002 && echo. && npm start"

:: Wait for frontend to start
timeout /t 10 /nobreak >nul

echo.
echo ========================================
echo  AGRIINTEL STARTUP COMPLETE
echo ========================================
echo.
echo Backend API:     http://localhost:3001/api
echo Frontend App:    http://localhost:3002
echo Health Check:    http://localhost:3001/health
echo.
echo LOGIN CREDENTIALS:
echo   Demo User:      Demo / 123 (BETA Access)
echo   Professional:   Pro / 123 (Full Access)
echo   Administrator:  admin / Admin@123 (Full Access)
echo.
echo [INFO] Opening AgriIntel in your default browser...
timeout /t 3 /nobreak >nul
start http://localhost:3002

echo.
echo [SUCCESS] AgriIntel is now running!
echo Press any key to view system status...
pause >nul

:: Show system status
echo.
echo ========================================
echo  SYSTEM STATUS
echo ========================================
echo.
echo Active Node.js processes:
tasklist /FI "IMAGENAME eq node.exe" /FO TABLE 2>nul
echo.
echo Active ports:
netstat -ano | findstr ":3001\|:3002" 2>nul
echo.
echo To stop AgriIntel, close both terminal windows or press Ctrl+C in each.
echo.
pause
