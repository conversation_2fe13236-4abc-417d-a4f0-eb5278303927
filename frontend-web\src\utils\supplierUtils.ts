/**
 * Supplier Utilities
 * Helper functions for supplier-related operations
 */

import { SupplierProductCategory } from '../types/commercial/suppliers';

/**
 * Get display color for supplier category
 */
export const getCategoryColor = (category: SupplierProductCategory): string => {
  const categoryColors: Record<SupplierProductCategory, string> = {
    'livestock-feed': '#4CAF50',
    'equipment': '#FF9800',
    'seeds': '#8BC34A',
    'fertilizer': '#795548',
    'chemicals': '#F44336',
    'irrigation': '#2196F3',
    'animal-health': '#E91E63',
    'fuel': '#9C27B0',
    'retail': '#607D8B',
    'financial': '#3F51B5',
    'insurance': '#009688',
    'other': '#757575'
  };

  return categoryColors[category] || '#757575';
};

/**
 * Get display name for supplier category
 */
export const getCategoryDisplayName = (category: SupplierProductCategory): string => {
  const categoryNames: Record<SupplierProductCategory, string> = {
    'livestock-feed': 'Livestock Feed',
    'equipment': 'Equipment',
    'seeds': 'Seeds',
    'fertilizer': 'Fertilizer',
    'chemicals': 'Chemicals',
    'irrigation': 'Irrigation',
    'animal-health': 'Animal Health',
    'fuel': 'Fuel',
    'retail': 'Retail',
    'financial': 'Financial',
    'insurance': 'Insurance',
    'other': 'Other'
  };

  return categoryNames[category] || 'Other';
};

/**
 * Get all available categories
 */
export const getAllCategories = (): SupplierProductCategory[] => {
  return [
    'livestock-feed',
    'equipment',
    'seeds',
    'fertilizer',
    'chemicals',
    'irrigation',
    'animal-health',
    'fuel',
    'retail',
    'financial',
    'insurance',
    'other'
  ];
};

/**
 * Filter suppliers by category
 */
export const filterSuppliersByCategory = (suppliers: any[], category: string): any[] => {
  if (category === 'all') {
    return suppliers;
  }
  
  return suppliers.filter(supplier => 
    supplier.categories && supplier.categories.includes(category)
  );
};

/**
 * Search suppliers by name or description
 */
export const searchSuppliers = (suppliers: any[], query: string): any[] => {
  if (!query.trim()) {
    return suppliers;
  }
  
  const searchTerm = query.toLowerCase();
  
  return suppliers.filter(supplier => 
    supplier.name.toLowerCase().includes(searchTerm) ||
    supplier.description.toLowerCase().includes(searchTerm) ||
    supplier.shortDescription?.toLowerCase().includes(searchTerm)
  );
};

/**
 * Sort suppliers by various criteria
 */
export const sortSuppliers = (suppliers: any[], sortBy: 'name' | 'rating' | 'established'): any[] => {
  return [...suppliers].sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return a.name.localeCompare(b.name);
      case 'rating':
        return (b.rating || 0) - (a.rating || 0);
      case 'established':
        return (b.established || 0) - (a.established || 0);
      default:
        return 0;
    }
  });
};
