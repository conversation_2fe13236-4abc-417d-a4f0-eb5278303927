import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, Box, Typography } from '@mui/material';
import { motion } from 'framer-motion';

interface HolographicCardProps {
  children: React.ReactNode;
  intensity?: number;
  glowColor?: string;
  borderGradient?: string;
  className?: string;
  onClick?: () => void;
}

const HolographicCard: React.FC<HolographicCardProps> = ({
  children,
  intensity = 0.5,
  glowColor = '#00D4FF',
  borderGradient = 'linear-gradient(135deg, #00D4FF, #7C3AED, #F59E0B)',
  className = '',
  onClick
}) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!cardRef.current) return;

    const rect = cardRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    setMousePosition({ x, y });
  };

  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    setMousePosition({ x: 0, y: 0 });
  };

  const calculateTilt = () => {
    if (!cardRef.current || !isHovered) return { rotateX: 0, rotateY: 0 };

    const rect = cardRef.current.getBoundingClientRect();
    const centerX = rect.width / 2;
    const centerY = rect.height / 2;
    
    const rotateX = (mousePosition.y - centerY) / centerY * -10 * intensity;
    const rotateY = (mousePosition.x - centerX) / centerX * 10 * intensity;
    
    return { rotateX, rotateY };
  };

  const { rotateX, rotateY } = calculateTilt();

  const cardVariants = {
    initial: {
      rotateX: 0,
      rotateY: 0,
      scale: 1,
    },
    hover: {
      rotateX,
      rotateY,
      scale: 1.02,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 20
      }
    }
  };

  const glowVariants = {
    initial: {
      opacity: 0,
      scale: 0.8,
    },
    hover: {
      opacity: isHovered ? 0.6 : 0,
      scale: isHovered ? 1.1 : 0.8,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    }
  };

  return (
    <Box
      sx={{
        position: 'relative',
        perspective: '1000px',
        transformStyle: 'preserve-3d',
      }}
    >
      {/* Holographic Glow Effect */}
      <motion.div
        variants={glowVariants}
        initial="initial"
        animate="hover"
        style={{
          position: 'absolute',
          top: '-10px',
          left: '-10px',
          right: '-10px',
          bottom: '-10px',
          background: `radial-gradient(circle at ${mousePosition.x}px ${mousePosition.y}px, ${glowColor}40, transparent 70%)`,
          borderRadius: '24px',
          filter: 'blur(20px)',
          zIndex: 0,
          pointerEvents: 'none',
        }}
      />

      {/* Animated Border */}
      <Box
        sx={{
          position: 'absolute',
          top: '-2px',
          left: '-2px',
          right: '-2px',
          bottom: '-2px',
          background: borderGradient,
          borderRadius: '22px',
          zIndex: 1,
          opacity: isHovered ? 1 : 0.3,
          transition: 'opacity 0.3s ease',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: borderGradient,
            borderRadius: '22px',
            animation: isHovered ? 'borderRotate 3s linear infinite' : 'none',
          },
          '@keyframes borderRotate': {
            '0%': { transform: 'rotate(0deg)' },
            '100%': { transform: 'rotate(360deg)' },
          },
        }}
      />

      {/* Main Card */}
      <motion.div
        ref={cardRef}
        variants={cardVariants}
        initial="initial"
        animate={isHovered ? "hover" : "initial"}
        onMouseMove={handleMouseMove}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={onClick}
        style={{
          position: 'relative',
          zIndex: 2,
          cursor: onClick ? 'pointer' : 'default',
        }}
      >
        <Card
          className={`agri-feature-card ${className}`}
          sx={{
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            borderRadius: '20px',
            boxShadow: isHovered 
              ? `0 20px 60px rgba(0, 0, 0, 0.3), 0 0 40px ${glowColor}40`
              : '0 8px 32px rgba(0, 0, 0, 0.1)',
            transition: 'all 0.3s ease',
            position: 'relative',
            overflow: 'hidden',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: '-100%',
              width: '100%',
              height: '2px',
              background: borderGradient,
              animation: isHovered ? 'shimmer 2s ease-in-out infinite' : 'none',
            },
            '@keyframes shimmer': {
              '0%': { left: '-100%' },
              '100%': { left: '100%' },
            },
          }}
        >
          {/* Holographic Reflection */}
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: `radial-gradient(circle at ${mousePosition.x}px ${mousePosition.y}px, rgba(255, 255, 255, 0.1), transparent 50%)`,
              opacity: isHovered ? 1 : 0,
              transition: 'opacity 0.3s ease',
              pointerEvents: 'none',
              borderRadius: '20px',
            }}
          />

          {/* Particle Effect */}
          {isHovered && (
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: `radial-gradient(circle at ${mousePosition.x}px ${mousePosition.y}px, ${glowColor}20, transparent 30%)`,
                animation: 'particleFloat 2s ease-in-out infinite',
                borderRadius: '20px',
                pointerEvents: 'none',
                '@keyframes particleFloat': {
                  '0%, 100%': { opacity: 0.2 },
                  '50%': { opacity: 0.6 },
                },
              }}
            />
          )}

          <CardContent sx={{ position: 'relative', zIndex: 1 }}>
            {children}
          </CardContent>
        </Card>
      </motion.div>

      {/* Neural Network Connections */}
      {isHovered && (
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            width: '200%',
            height: '200%',
            transform: 'translate(-50%, -50%)',
            pointerEvents: 'none',
            zIndex: 0,
            '&::before': {
              content: '""',
              position: 'absolute',
              top: '50%',
              left: '50%',
              width: '2px',
              height: '100px',
              background: `linear-gradient(to bottom, ${glowColor}, transparent)`,
              transform: 'translate(-50%, -50%) rotate(45deg)',
              animation: 'connectionPulse 1.5s ease-in-out infinite',
            },
            '&::after': {
              content: '""',
              position: 'absolute',
              top: '50%',
              left: '50%',
              width: '2px',
              height: '100px',
              background: `linear-gradient(to bottom, ${glowColor}, transparent)`,
              transform: 'translate(-50%, -50%) rotate(-45deg)',
              animation: 'connectionPulse 1.5s ease-in-out infinite 0.5s',
            },
            '@keyframes connectionPulse': {
              '0%, 100%': { opacity: 0 },
              '50%': { opacity: 0.8 },
            },
          }}
        />
      )}
    </Box>
  );
};

export default HolographicCard;
