/**
 * AgriIntel Landing Page Styles - Modern 2026 Design
 * Professional agricultural technology platform with glassmorphism effects
 */

/* ===== MODERN TAB PANEL STYLES ===== */
.agriintel-tabpanel {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg,
    rgba(21, 101, 192, 0.95) 0%,
    rgba(46, 125, 50, 0.90) 25%,
    rgba(245, 124, 0, 0.85) 50%,
    rgba(46, 125, 50, 0.90) 75%,
    rgba(21, 101, 192, 0.95) 100%
  );
  background-size: 400% 400%;
  animation: modernGradientShift 15s ease infinite;
  position: relative;
  overflow: hidden;
}

.agriintel-tabpanel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    url('https://images.pexels.com/photos/1595104/pexels-photo-1595104.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&dpr=1'),
    url('https://images.pexels.com/photos/2132180/pexels-photo-2132180.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&dpr=1');
  background-size: cover, cover;
  background-position: center, center;
  background-blend-mode: overlay, multiply;
  opacity: 0.3;
  z-index: 1;
}

.agriintel-tabpanel-content {
  position: relative;
  z-index: 2;
  padding: 64px 32px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.05);
  border-radius: 24px;
  margin: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

/* ===== MODERN NAVIGATION STYLES ===== */
.agriintel-nav-container {
  border-bottom: 3px solid #F57C00;
  background: linear-gradient(90deg,
    rgba(21, 101, 192, 0.95) 0%,
    rgba(46, 125, 50, 0.95) 50%,
    rgba(245, 124, 0, 0.95) 100%
  );
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  position: sticky;
  top: 0;
  z-index: 1000;

/* ===== MODERN ANIMATIONS ===== */
@keyframes modernGradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  25% {
    background-position: 100% 50%;
  }
  50% {
    background-position: 100% 100%;
  }
  75% {
    background-position: 0% 100%;
  }
}

@keyframes navGlow {
  0%, 100% {
    box-shadow: 0 8px 32px rgba(21, 101, 192, 0.3);
  }
  50% {
    box-shadow: 0 12px 48px rgba(245, 124, 0, 0.4);
  }
}

@keyframes cardFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(245, 124, 0, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(245, 124, 0, 0.6);
  }
}

/* ===== MODERN CARD DESIGNS ===== */
.agriintel-feature-card {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  margin: 1rem;
  transition: all 0.3s ease;
  animation: cardFloat 6s ease-in-out infinite;
}

.agriintel-feature-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 25px 80px rgba(245, 124, 0, 0.3);
  border-color: rgba(245, 124, 0, 0.5);
}

.agriintel-pricing-card {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.1) 100%
  );
  -webkit-backdrop-filter: blur(25px);
  backdrop-filter: blur(25px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  padding: 2.5rem;
  margin: 1rem;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.agriintel-pricing-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg,
    #1565C0 0%,
    #2E7D32 50%,
    #F57C00 100%
  );
  animation: pulseGlow 2s ease infinite;
}

.agriintel-pricing-card:hover {
  transform: translateY(-15px) scale(1.03);
  box-shadow: 0 30px 100px rgba(21, 101, 192, 0.4);
  border-color: rgba(21, 101, 192, 0.6);
}

/* ===== ENHANCED TYPOGRAPHY ===== */
.agriintel-hero-title {
  font-size: 4rem !important;
  font-weight: 800 !important;
  background: linear-gradient(135deg,
    #FFFFFF 0%,
    #F57C00 50%,
    #FFFFFF 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  animation: pulseGlow 3s ease infinite;
}

.agriintel-hero-subtitle {
  font-size: 1.5rem !important;
  font-weight: 500 !important;
  color: rgba(255, 255, 255, 0.9) !important;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  margin-top: 1rem !important;
}

/* ===== MODERN BUTTONS ===== */
.agriintel-cta-button {
  background: linear-gradient(135deg, #F57C00, #FF9800) !important;
  border: none !important;
  border-radius: 16px !important;
  padding: 1rem 2.5rem !important;
  font-size: 1.2rem !important;
  font-weight: 700 !important;
  color: white !important;
  box-shadow: 0 8px 32px rgba(245, 124, 0, 0.4) !important;
  transition: all 0.3s ease !important;
  text-transform: none !important;
}

.agriintel-cta-button:hover {
  background: linear-gradient(135deg, #E65100, #F57C00) !important;
  transform: translateY(-4px) scale(1.05) !important;
  box-shadow: 0 12px 48px rgba(245, 124, 0, 0.6) !important;
}

.agriintel-secondary-button {
  background: transparent !important;
  border: 2px solid rgba(255, 255, 255, 0.8) !important;
  border-radius: 16px !important;
  padding: 1rem 2.5rem !important;
  font-size: 1.2rem !important;
  font-weight: 600 !important;
  color: white !important;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease !important;
  text-transform: none !important;
}

.agriintel-secondary-button:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  border-color: #F57C00 !important;
  color: #F57C00 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(255, 255, 255, 0.2) !important;
}
  box-shadow: 0 8px 32px rgba(21, 101, 192, 0.3);
  animation: navGlow 3s ease infinite;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.agriintel-tabs {
  min-width: 160px;
  color: #2E7D32;
  font-weight: 600;
  text-transform: none;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.agriintel-tabs:hover {
  background-color: rgba(46, 125, 50, 0.1);
  color: #1B5E20;
}

.agriintel-tabs.Mui-selected {
  color: #4CAF50;
  font-weight: 700;
}

/* ===== FEATURE STYLES ===== */
.agri-feature-icon {
  margin-bottom: 16px;
  display: flex;
  justify-content: center;
}

.agri-feature-title {
  color: white !important;
  font-weight: 700 !important;
  margin-bottom: 16px !important;
}

.agri-feature-description {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* ===== PRICING STYLES ===== */
.pricing-container {
  padding: 48px 0 64px 0;
}

.pricing-grid {
  margin-top: 32px;
}

.pricing-card {
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  transition: all 0.3s ease;
}

.pricing-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.pricing-card-content {
  padding: 32px;
  text-align: center;
}

.pricing-chip-beta {
  background: var(--agri-beta-primary) !important;
  color: white !important;
  font-weight: 700 !important;
  margin-bottom: 16px !important;
}

.pricing-chip-pro {
  background: var(--agri-pro-primary) !important;
  color: white !important;
  font-weight: 700 !important;
  margin-bottom: 16px !important;
}

.pricing-title {
  color: var(--agri-white) !important;
  font-weight: 800 !important;
  margin-bottom: 8px !important;
}

.pricing-amount-beta {
  color: var(--agri-beta-primary) !important;
  font-weight: 900 !important;
  margin-bottom: 8px !important;
}

.pricing-amount-pro {
  color: var(--agri-pro-primary) !important;
  font-weight: 900 !important;
  margin-bottom: 8px !important;
}

.pricing-subtitle {
  color: var(--agri-white) !important;
  opacity: 0.8;
  margin-bottom: 24px !important;
}

.pricing-features {
  text-align: left;
  margin-bottom: 24px;
}

.pricing-feature-item {
  color: var(--agri-white) !important;
  margin-bottom: 8px !important;
  display: flex;
  align-items: center;
}

.pricing-feature-icon-beta {
  color: var(--agri-beta-primary) !important;
  margin-right: 8px !important;
  font-size: 1rem !important;
}

.pricing-feature-icon-pro {
  color: var(--agri-pro-primary) !important;
  margin-right: 8px !important;
  font-size: 1rem !important;
}

.pricing-button-beta {
  background: var(--agri-beta-primary) !important;
  color: white !important;
  font-weight: 700 !important;
  padding: 12px 32px !important;
  border-radius: 8px !important;
  text-transform: none !important;
  font-size: 1.1rem !important;
}

.pricing-button-beta:hover {
  background: var(--agri-beta-secondary) !important;
  transform: translateY(-2px);
}

.pricing-button-pro {
  background: var(--agri-pro-primary) !important;
  color: white !important;
  font-weight: 700 !important;
  padding: 12px 32px !important;
  border-radius: 8px !important;
  text-transform: none !important;
  font-size: 1.1rem !important;
}

.pricing-button-pro:hover {
  background: var(--agri-pro-secondary) !important;
  transform: translateY(-2px);
}

/* ===== CONTACT STYLES ===== */
.contact-container {
  padding: 48px 0 64px 0;
}

.contact-card {
  padding: 24px;
  height: 100%;
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
}

.contact-title {
  margin-bottom: 16px;
  color: #2E7D32 !important;
}

.contact-info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.contact-icon {
  margin-right: 16px;
  color: #4CAF50 !important;
}

.contact-section-title {
  margin-top: 24px;
  margin-bottom: 8px;
  color: #2E7D32 !important;
}

.contact-description {
  margin-bottom: 16px;
}

.whatsapp-button {
  background-color: #25D366 !important;
  color: white !important;
  margin-bottom: 16px !important;
  font-weight: 600 !important;
}

.whatsapp-button:hover {
  background-color: #128C7E !important;
}

.contact-note {
  color: #666 !important;
}

.contact-item-text {
  font-size: 0.9rem;
  font-weight: 500;
}

/* ===== FOOTER STYLES ===== */
.footer-badge-nature {
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%) !important;
  color: white !important;
  font-weight: 600 !important;
  margin: 4px !important;
}

.footer-badge-tech {
  background: linear-gradient(135deg, #1565C0 0%, #0D47A1 100%) !important;
  color: white !important;
  font-weight: 600 !important;
  margin: 4px !important;
}

.footer-badge-innovation {
  background: linear-gradient(135deg, #F57C00 0%, #E65100 100%) !important;
  color: white !important;
  font-weight: 600 !important;
  margin: 4px !important;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .agriintel-tabpanel-content {
    padding: 32px 16px;
  }
  
  .pricing-container {
    padding: 32px 0 48px 0;
  }
  
  .contact-container {
    padding: 32px 0 48px 0;
  }
  
  .pricing-card-content {
    padding: 24px;
  }
  
  .contact-card {
    padding: 20px;
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  .pricing-card,
  .pricing-button-beta,
  .pricing-button-pro,
  .agriintel-tabs {
    transition: none;
  }
  
  .pricing-card:hover,
  .pricing-button-beta:hover,
  .pricing-button-pro:hover {
    transform: none;
  }
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
  .agriintel-tabpanel::before {
    background: rgba(0, 0, 0, 0.8);
  }
  
  .pricing-card,
  .contact-card {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid #000;
  }
  
  .agri-feature-title,
  .agri-feature-description {
    color: white !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  }
}
