/**
 * AgriIntel Landing Page Styles
 * External CSS to replace inline styles for better maintainability
 */

/* ===== TAB PANEL STYLES ===== */
.agriintel-tabpanel {
  width: 100%;
  min-height: 80vh;
  background-image: url('https://images.pexels.com/photos/1595104/pexels-photo-1595104.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&dpr=1');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  position: relative;
}

.agriintel-tabpanel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(21, 101, 192, 0.85) 0%, 
    rgba(46, 125, 50, 0.85) 50%, 
    rgba(245, 124, 0, 0.85) 100%
  );
  z-index: 1;
}

.agriintel-tabpanel-content {
  position: relative;
  z-index: 2;
  padding: 48px 24px;
  min-height: 80vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* ===== NAVIGATION STYLES ===== */
.agriintel-nav-container {
  border-bottom: 2px solid #4CAF50;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.agriintel-tabs {
  min-width: 160px;
  color: #2E7D32;
  font-weight: 600;
  text-transform: none;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.agriintel-tabs:hover {
  background-color: rgba(46, 125, 50, 0.1);
  color: #1B5E20;
}

.agriintel-tabs.Mui-selected {
  color: #4CAF50;
  font-weight: 700;
}

/* ===== FEATURE STYLES ===== */
.agri-feature-icon {
  margin-bottom: 16px;
  display: flex;
  justify-content: center;
}

.agri-feature-title {
  color: white !important;
  font-weight: 700 !important;
  margin-bottom: 16px !important;
}

.agri-feature-description {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* ===== PRICING STYLES ===== */
.pricing-container {
  padding: 48px 0 64px 0;
}

.pricing-grid {
  margin-top: 32px;
}

.pricing-card {
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  transition: all 0.3s ease;
}

.pricing-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.pricing-card-content {
  padding: 32px;
  text-align: center;
}

.pricing-chip-beta {
  background: var(--agri-beta-primary) !important;
  color: white !important;
  font-weight: 700 !important;
  margin-bottom: 16px !important;
}

.pricing-chip-pro {
  background: var(--agri-pro-primary) !important;
  color: white !important;
  font-weight: 700 !important;
  margin-bottom: 16px !important;
}

.pricing-title {
  color: var(--agri-white) !important;
  font-weight: 800 !important;
  margin-bottom: 8px !important;
}

.pricing-amount-beta {
  color: var(--agri-beta-primary) !important;
  font-weight: 900 !important;
  margin-bottom: 8px !important;
}

.pricing-amount-pro {
  color: var(--agri-pro-primary) !important;
  font-weight: 900 !important;
  margin-bottom: 8px !important;
}

.pricing-subtitle {
  color: var(--agri-white) !important;
  opacity: 0.8;
  margin-bottom: 24px !important;
}

.pricing-features {
  text-align: left;
  margin-bottom: 24px;
}

.pricing-feature-item {
  color: var(--agri-white) !important;
  margin-bottom: 8px !important;
  display: flex;
  align-items: center;
}

.pricing-feature-icon-beta {
  color: var(--agri-beta-primary) !important;
  margin-right: 8px !important;
  font-size: 1rem !important;
}

.pricing-feature-icon-pro {
  color: var(--agri-pro-primary) !important;
  margin-right: 8px !important;
  font-size: 1rem !important;
}

.pricing-button-beta {
  background: var(--agri-beta-primary) !important;
  color: white !important;
  font-weight: 700 !important;
  padding: 12px 32px !important;
  border-radius: 8px !important;
  text-transform: none !important;
  font-size: 1.1rem !important;
}

.pricing-button-beta:hover {
  background: var(--agri-beta-secondary) !important;
  transform: translateY(-2px);
}

.pricing-button-pro {
  background: var(--agri-pro-primary) !important;
  color: white !important;
  font-weight: 700 !important;
  padding: 12px 32px !important;
  border-radius: 8px !important;
  text-transform: none !important;
  font-size: 1.1rem !important;
}

.pricing-button-pro:hover {
  background: var(--agri-pro-secondary) !important;
  transform: translateY(-2px);
}

/* ===== CONTACT STYLES ===== */
.contact-container {
  padding: 48px 0 64px 0;
}

.contact-card {
  padding: 24px;
  height: 100%;
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
}

.contact-title {
  margin-bottom: 16px;
  color: #2E7D32 !important;
}

.contact-info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.contact-icon {
  margin-right: 16px;
  color: #4CAF50 !important;
}

.contact-section-title {
  margin-top: 24px;
  margin-bottom: 8px;
  color: #2E7D32 !important;
}

.contact-description {
  margin-bottom: 16px;
}

.whatsapp-button {
  background-color: #25D366 !important;
  color: white !important;
  margin-bottom: 16px !important;
  font-weight: 600 !important;
}

.whatsapp-button:hover {
  background-color: #128C7E !important;
}

.contact-note {
  color: #666 !important;
}

.contact-item-text {
  font-size: 0.9rem;
  font-weight: 500;
}

/* ===== FOOTER STYLES ===== */
.footer-badge-nature {
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%) !important;
  color: white !important;
  font-weight: 600 !important;
  margin: 4px !important;
}

.footer-badge-tech {
  background: linear-gradient(135deg, #1565C0 0%, #0D47A1 100%) !important;
  color: white !important;
  font-weight: 600 !important;
  margin: 4px !important;
}

.footer-badge-innovation {
  background: linear-gradient(135deg, #F57C00 0%, #E65100 100%) !important;
  color: white !important;
  font-weight: 600 !important;
  margin: 4px !important;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .agriintel-tabpanel-content {
    padding: 32px 16px;
  }
  
  .pricing-container {
    padding: 32px 0 48px 0;
  }
  
  .contact-container {
    padding: 32px 0 48px 0;
  }
  
  .pricing-card-content {
    padding: 24px;
  }
  
  .contact-card {
    padding: 20px;
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  .pricing-card,
  .pricing-button-beta,
  .pricing-button-pro,
  .agriintel-tabs {
    transition: none;
  }
  
  .pricing-card:hover,
  .pricing-button-beta:hover,
  .pricing-button-pro:hover {
    transform: none;
  }
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
  .agriintel-tabpanel::before {
    background: rgba(0, 0, 0, 0.8);
  }
  
  .pricing-card,
  .contact-card {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid #000;
  }
  
  .agri-feature-title,
  .agri-feature-description {
    color: white !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  }
}
