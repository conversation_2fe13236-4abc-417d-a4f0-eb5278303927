/*
 * ENHANCED LANGUAGE SELECTOR STYLES
 * Professional multi-language selector with futuristic design
 */

/* Futuristic Language Selector */
.enhanced-lang-selector {
  position: relative;
  z-index: 1000;
}

.enhanced-lang-button {
  background: rgba(0, 212, 255, 0.1) !important;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 212, 255, 0.3) !important;
  border-radius: 12px !important;
  color: #00D4FF !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.enhanced-lang-button:hover {
  background: rgba(0, 212, 255, 0.2) !important;
  border-color: #00D4FF !important;
  transform: translateY(-2px);
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.4), 0 0 40px rgba(0, 212, 255, 0.2) !important;
}

.enhanced-lang-menu {
  background: rgba(15, 23, 42, 0.9) !important;
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 212, 255, 0.3) !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 212, 255, 0.2) !important;
  margin-top: 8px !important;
  min-width: 280px !important;
}

.enhanced-lang-header {
  padding: 8px 16px !important;
  border-bottom: 1px solid rgba(0, 212, 255, 0.2) !important;
}

.enhanced-lang-header-text {
  color: #00D4FF !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 1px !important;
}

.enhanced-lang-item {
  color: white !important;
  padding: 12px 16px !important;
  border-left: 3px solid transparent !important;
}

.enhanced-lang-item:hover {
  background: rgba(0, 212, 255, 0.1) !important;
}

.enhanced-lang-item.selected {
  border-left-color: #00D4FF !important;
}

.enhanced-lang-item-icon {
  color: inherit !important;
  min-width: 40px !important;
}

.enhanced-lang-item-text {
  font-weight: 400 !important;
  color: white !important;
}

.enhanced-lang-item-text.selected {
  font-weight: 600 !important;
  color: #00D4FF !important;
}

.enhanced-lang-check {
  color: #00D4FF !important;
  margin-left: 8px !important;
}

.enhanced-lang-flag {
  font-size: 1.2rem !important;
}

/* Additional Languages Section */
.enhanced-lang-additional {
  padding: 8px 16px !important;
  border-top: 1px solid rgba(0, 212, 255, 0.2) !important;
}

.enhanced-lang-additional-text {
  color: rgba(255, 255, 255, 0.6) !important;
  font-weight: 500 !important;
  text-transform: uppercase !important;
  letter-spacing: 1px !important;
}

.enhanced-lang-item-additional {
  color: rgba(255, 255, 255, 0.8) !important;
  padding: 8px 16px !important;
  border-left: 3px solid transparent !important;
}

.enhanced-lang-item-additional:hover {
  background: rgba(124, 58, 237, 0.1) !important;
}

.enhanced-lang-item-additional.selected {
  border-left-color: #7C3AED !important;
}

.enhanced-lang-item-additional-text {
  font-weight: 400 !important;
  color: rgba(255, 255, 255, 0.8) !important;
}

.enhanced-lang-item-additional-text.selected {
  font-weight: 600 !important;
  color: #7C3AED !important;
}

.enhanced-lang-check-additional {
  color: #7C3AED !important;
  margin-left: 8px !important;
}

/* Compact Variant */
.enhanced-lang-chip {
  background: rgba(25, 118, 210, 0.1) !important;
  color: #1976d2 !important;
  border: 1px solid rgba(25, 118, 210, 0.3) !important;
}

.enhanced-lang-chip:hover {
  background: rgba(25, 118, 210, 0.2) !important;
}

/* Standard Variant */
.enhanced-lang-form {
  min-width: 180px !important;
}

.enhanced-lang-label {
  margin-bottom: 8px !important;
  color: rgba(0, 0, 0, 0.6) !important;
}

.enhanced-lang-select {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.enhanced-lang-option {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .enhanced-lang-menu {
    min-width: 240px !important;
  }
  
  .enhanced-lang-item,
  .enhanced-lang-item-additional {
    padding: 10px 12px !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .enhanced-lang-button {
    background: rgba(0, 212, 255, 0.2) !important;
    border-color: #00D4FF !important;
  }
  
  .enhanced-lang-menu {
    background: rgba(0, 0, 0, 0.9) !important;
    border-color: #00D4FF !important;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .enhanced-lang-button,
  .enhanced-lang-item,
  .enhanced-lang-item-additional {
    transition: none !important;
  }
  
  .enhanced-lang-button:hover {
    transform: none !important;
  }
}
