/**
 * Production Readiness Comprehensive Audit
 * Zero tolerance scan for LIVE deployment preparation
 */

export interface ProductionIssue {
  severity: 'critical' | 'high' | 'medium' | 'low';
  category: string;
  description: string;
  location: string;
  fix: string;
}

export interface ProductionAudit {
  critical: ProductionIssue[];
  high: ProductionIssue[];
  medium: ProductionIssue[];
  low: ProductionIssue[];
  summary: {
    totalIssues: number;
    criticalIssues: number;
    highIssues: number;
    readyForProduction: boolean;
    blockers: string[];
  };
}

/**
 * Check for broken links and missing resources
 */
function checkBrokenLinks(): ProductionIssue[] {
  const issues: ProductionIssue[] = [];

  // Check for broken images
  const images = document.querySelectorAll('img');
  images.forEach((img, index) => {
    if (img.naturalWidth === 0 && img.complete) {
      issues.push({
        severity: 'high',
        category: 'Broken Resources',
        description: `Broken image: ${img.src}`,
        location: `Image element #${index}`,
        fix: 'Replace with working image URL or add fallback'
      });
    }
  });

  // Check for broken links
  const links = document.querySelectorAll('a[href]');
  links.forEach((link, index) => {
    const href = link.getAttribute('href');
    if (href && href.startsWith('#') && !document.querySelector(href)) {
      issues.push({
        severity: 'medium',
        category: 'Broken Links',
        description: `Broken anchor link: ${href}`,
        location: `Link element #${index}`,
        fix: 'Fix anchor target or remove link'
      });
    }
  });

  return issues;
}

/**
 * Check for non-functional components
 */
function checkNonFunctionalComponents(): ProductionIssue[] {
  const issues: ProductionIssue[] = [];

  // Check for disabled buttons that should be functional
  const disabledButtons = document.querySelectorAll('button[disabled]:not([data-loading])');
  if (disabledButtons.length > 5) { // Allow some disabled buttons for valid reasons
    issues.push({
      severity: 'high',
      category: 'Non-functional Components',
      description: `${disabledButtons.length} buttons are disabled`,
      location: 'Multiple button elements',
      fix: 'Review and enable functional buttons'
    });
  }

  // Check for empty data containers
  const dataContainers = document.querySelectorAll('.data-container, .table-body, .list-container');
  dataContainers.forEach((container, index) => {
    if (container.children.length === 0) {
      issues.push({
        severity: 'medium',
        category: 'Empty Components',
        description: 'Empty data container found',
        location: `Data container #${index}`,
        fix: 'Populate with data or add loading/empty state'
      });
    }
  });

  // Check for error states
  const errorElements = document.querySelectorAll('.error, .MuiAlert-standardError, [role="alert"]');
  errorElements.forEach((error, index) => {
    issues.push({
      severity: 'critical',
      category: 'Error States',
      description: `Error message displayed: ${error.textContent?.slice(0, 100)}`,
      location: `Error element #${index}`,
      fix: 'Resolve underlying error or improve error handling'
    });
  });

  return issues;
}

/**
 * Check visual inconsistencies
 */
function checkVisualInconsistencies(): ProductionIssue[] {
  const issues: ProductionIssue[] = [];

  // Check for missing styles
  const unstyledElements = document.querySelectorAll('*:not([class]):not([style])');
  if (unstyledElements.length > 50) { // Allow some unstyled elements
    issues.push({
      severity: 'low',
      category: 'Visual Inconsistencies',
      description: `${unstyledElements.length} elements without styling`,
      location: 'Multiple elements',
      fix: 'Add appropriate CSS classes or styles'
    });
  }

  // Check for white backgrounds that might be covering content
  const whiteBackgrounds = document.querySelectorAll('[style*="background: #FFFFFF"], [style*="background-color: white"]');
  if (whiteBackgrounds.length > 0) {
    issues.push({
      severity: 'medium',
      category: 'Visual Inconsistencies',
      description: `${whiteBackgrounds.length} elements with white backgrounds`,
      location: 'Multiple elements',
      fix: 'Review and apply theme-appropriate backgrounds'
    });
  }

  return issues;
}

/**
 * Check BETA tier compliance
 */
function checkBetaTierCompliance(): ProductionIssue[] {
  const issues: ProductionIssue[] = [];

  // Check for BETA badges
  const betaBadges = document.querySelectorAll('.beta-badge, [data-tier="beta"], .beta-chip');
  if (betaBadges.length === 0) {
    issues.push({
      severity: 'high',
      category: 'BETA Compliance',
      description: 'No BETA tier indicators found',
      location: 'Application-wide',
      fix: 'Add BETA badges and tier indicators'
    });
  }

  // Check for upgrade prompts
  const upgradePrompts = document.querySelectorAll('.upgrade-prompt, [data-upgrade], .upgrade-button');
  if (upgradePrompts.length === 0) {
    issues.push({
      severity: 'medium',
      category: 'BETA Compliance',
      description: 'No upgrade prompts found',
      location: 'Application-wide',
      fix: 'Add upgrade prompts for Professional features'
    });
  }

  // Check for animal limit indicators
  const limitIndicators = document.querySelectorAll('[data-limit], .limit-indicator, .usage-progress');
  if (limitIndicators.length === 0) {
    issues.push({
      severity: 'high',
      category: 'BETA Compliance',
      description: 'No animal limit indicators found',
      location: 'Animals module',
      fix: 'Add 50-animal limit indicators and progress bars'
    });
  }

  return issues;
}

/**
 * Check data population
 */
function checkDataPopulation(): ProductionIssue[] {
  const issues: ProductionIssue[] = [];

  // Check for data in key modules
  const animalData = document.querySelectorAll('.animal-row, .animal-card, [data-animal]');
  if (animalData.length < 2) {
    issues.push({
      severity: 'critical',
      category: 'Data Population',
      description: `Only ${animalData.length} animal records found (expected 2)`,
      location: 'Animals module',
      fix: 'Populate with 2 realistic South African livestock records'
    });
  }

  const healthData = document.querySelectorAll('.health-record, .health-row, [data-health]');
  if (healthData.length < 2) {
    issues.push({
      severity: 'critical',
      category: 'Data Population',
      description: `Only ${healthData.length} health records found (expected 2)`,
      location: 'Health module',
      fix: 'Populate with 2 health records linked to animals'
    });
  }

  const feedingData = document.querySelectorAll('.feeding-record, .feeding-row, [data-feeding]');
  if (feedingData.length < 2) {
    issues.push({
      severity: 'critical',
      category: 'Data Population',
      description: `Only ${feedingData.length} feeding records found (expected 2)`,
      location: 'Feeding module',
      fix: 'Populate with 2 feeding records linked to animals'
    });
  }

  const financialData = document.querySelectorAll('.financial-record, .financial-row, [data-financial]');
  if (financialData.length < 2) {
    issues.push({
      severity: 'critical',
      category: 'Data Population',
      description: `Only ${financialData.length} financial records found (expected 2)`,
      location: 'Financial module',
      fix: 'Populate with 2 financial records linked to animals'
    });
  }

  return issues;
}

/**
 * Check performance issues
 */
function checkPerformanceIssues(): ProductionIssue[] {
  const issues: ProductionIssue[] = [];

  // Check for excessive DOM elements
  const totalElements = document.querySelectorAll('*').length;
  if (totalElements > 5000) {
    issues.push({
      severity: 'medium',
      category: 'Performance',
      description: `${totalElements} DOM elements (may impact performance)`,
      location: 'Application-wide',
      fix: 'Optimize component rendering and virtualization'
    });
  }

  // Check for large images
  const largeImages = document.querySelectorAll('img');
  largeImages.forEach((img, index) => {
    if (img.naturalWidth > 2000 || img.naturalHeight > 2000) {
      issues.push({
        severity: 'low',
        category: 'Performance',
        description: `Large image: ${img.naturalWidth}x${img.naturalHeight}`,
        location: `Image #${index}`,
        fix: 'Optimize image size and format'
      });
    }
  });

  return issues;
}

/**
 * Run comprehensive production readiness audit
 */
export async function runProductionReadinessAudit(): Promise<ProductionAudit> {
  console.log('🔍 Starting Production Readiness Audit...');

  const allIssues: ProductionIssue[] = [
    ...checkBrokenLinks(),
    ...checkNonFunctionalComponents(),
    ...checkVisualInconsistencies(),
    ...checkBetaTierCompliance(),
    ...checkDataPopulation(),
    ...checkPerformanceIssues()
  ];

  const critical = allIssues.filter(i => i.severity === 'critical');
  const high = allIssues.filter(i => i.severity === 'high');
  const medium = allIssues.filter(i => i.severity === 'medium');
  const low = allIssues.filter(i => i.severity === 'low');

  const blockers = critical.map(i => i.description);
  const readyForProduction = critical.length === 0 && high.length === 0;

  const audit: ProductionAudit = {
    critical,
    high,
    medium,
    low,
    summary: {
      totalIssues: allIssues.length,
      criticalIssues: critical.length,
      highIssues: high.length,
      readyForProduction,
      blockers
    }
  };

  console.log('✅ Production Readiness Audit Complete:', audit.summary);
  return audit;
}

/**
 * Display detailed production audit results
 */
export function displayProductionAuditResults(audit: ProductionAudit) {
  console.group('🚨 Production Readiness Audit Results');

  if (audit.critical.length > 0) {
    console.group('🔴 CRITICAL ISSUES (MUST FIX)');
    audit.critical.forEach(issue => {
      console.log(`❌ ${issue.description}`);
      console.log(`   Location: ${issue.location}`);
      console.log(`   Fix: ${issue.fix}`);
    });
    console.groupEnd();
  }

  if (audit.high.length > 0) {
    console.group('🟠 HIGH PRIORITY ISSUES');
    audit.high.forEach(issue => {
      console.log(`⚠️ ${issue.description}`);
      console.log(`   Location: ${issue.location}`);
      console.log(`   Fix: ${issue.fix}`);
    });
    console.groupEnd();
  }

  if (audit.medium.length > 0) {
    console.group('🟡 MEDIUM PRIORITY ISSUES');
    audit.medium.forEach(issue => {
      console.log(`⚡ ${issue.description}`);
    });
    console.groupEnd();
  }

  console.group('📊 Summary');
  console.log(`Ready for Production: ${audit.summary.readyForProduction ? '✅ YES' : '❌ NO'}`);
  console.log(`Total Issues: ${audit.summary.totalIssues}`);
  console.log(`Critical: ${audit.summary.criticalIssues}`);
  console.log(`High: ${audit.summary.highIssues}`);
  
  if (audit.summary.blockers.length > 0) {
    console.log('🚫 Production Blockers:');
    audit.summary.blockers.forEach(blocker => console.log(`   - ${blocker}`));
  }
  console.groupEnd();

  console.groupEnd();
}

// Export for global access
(window as any).productionAudit = {
  runProductionReadinessAudit,
  displayProductionAuditResults
};
