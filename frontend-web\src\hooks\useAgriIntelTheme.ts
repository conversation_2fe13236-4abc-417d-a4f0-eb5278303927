/**
 * Enhanced AgriIntel Theme Hook
 * Provides comprehensive theme integration for all components
 */

import { useContext } from 'react';
import { useAgriIntelTheme as useBaseTheme } from '../components/theme/AgriIntelThemeProvider';

export interface AgriIntelThemeStyles {
  // Card styles
  card: React.CSSProperties;
  cardHover: React.CSSProperties;
  
  // But<PERSON> styles
  primaryButton: React.CSSProperties;
  secondaryButton: React.CSSProperties;
  outlinedButton: React.CSSProperties;
  
  // Input styles
  textField: React.CSSProperties;
  select: React.CSSProperties;
  
  // Layout styles
  container: React.CSSProperties;
  header: React.CSSProperties;
  sidebar: React.CSSProperties;
  
  // Background styles
  pageBackground: React.CSSProperties;
  sectionBackground: React.CSSProperties;
  
  // Text styles
  primaryText: React.CSSProperties;
  secondaryText: React.CSSProperties;
  
  // Module-specific styles
  moduleCard: React.CSSProperties;
  moduleHeader: React.CSSProperties;
}

export const useAgriIntelTheme = () => {
  const baseTheme = useBaseTheme();
  
  // Generate comprehensive styles based on current theme
  const styles: AgriIntelThemeStyles = {
    card: {
      background: 'rgba(255, 255, 255, 0.1)',
      backdropFilter: 'blur(20px)',
      WebkitBackdropFilter: 'blur(20px)',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      borderRadius: '16px',
      padding: '24px',
      color: '#ffffff',
      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
    },
    
    cardHover: {
      transform: 'translateY(-4px)',
      boxShadow: '0 20px 40px rgba(0, 0, 0, 0.4)',
      background: 'rgba(255, 255, 255, 0.15)',
    },
    
    primaryButton: {
      background: baseTheme.theme.gradients.primary,
      color: '#ffffff',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      borderRadius: '12px',
      padding: '12px 24px',
      fontSize: '1rem',
      fontWeight: 600,
      textTransform: 'none' as const,
      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      boxShadow: '0 8px 20px rgba(0, 0, 0, 0.3)',
      cursor: 'pointer',
    },
    
    secondaryButton: {
      background: 'rgba(255, 255, 255, 0.1)',
      color: '#ffffff',
      border: '2px solid rgba(255, 255, 255, 0.3)',
      borderRadius: '12px',
      padding: '12px 24px',
      fontSize: '1rem',
      fontWeight: 600,
      textTransform: 'none' as const,
      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      cursor: 'pointer',
    },
    
    outlinedButton: {
      background: 'transparent',
      color: '#ffffff',
      border: '2px solid rgba(255, 255, 255, 0.3)',
      borderRadius: '12px',
      padding: '12px 24px',
      fontSize: '1rem',
      fontWeight: 600,
      textTransform: 'none' as const,
      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      cursor: 'pointer',
    },
    
    textField: {
      background: 'rgba(255, 255, 255, 0.1)',
      backdropFilter: 'blur(10px)',
      WebkitBackdropFilter: 'blur(10px)',
      border: '1px solid rgba(255, 255, 255, 0.3)',
      borderRadius: '12px',
      padding: '12px 16px',
      color: '#ffffff',
      fontSize: '1rem',
      transition: 'all 0.3s ease',
    },
    
    select: {
      background: 'rgba(255, 255, 255, 0.1)',
      backdropFilter: 'blur(10px)',
      WebkitBackdropFilter: 'blur(10px)',
      border: '1px solid rgba(255, 255, 255, 0.3)',
      borderRadius: '12px',
      color: '#ffffff',
      fontSize: '1rem',
    },
    
    container: {
      background: 'transparent',
      color: '#ffffff',
      minHeight: '100vh',
    },
    
    header: {
      background: 'rgba(255, 255, 255, 0.1)',
      backdropFilter: 'blur(20px)',
      WebkitBackdropFilter: 'blur(20px)',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      borderRadius: '16px',
      padding: '16px 24px',
      color: '#ffffff',
      marginBottom: '24px',
    },
    
    sidebar: {
      background: 'rgba(255, 255, 255, 0.1)',
      backdropFilter: 'blur(20px)',
      WebkitBackdropFilter: 'blur(20px)',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      color: '#ffffff',
    },
    
    pageBackground: {
      background: baseTheme.theme.gradients.primary,
      backgroundAttachment: 'fixed',
      minHeight: '100vh',
      color: '#ffffff',
    },
    
    sectionBackground: {
      background: 'rgba(255, 255, 255, 0.05)',
      backdropFilter: 'blur(10px)',
      WebkitBackdropFilter: 'blur(10px)',
      border: '1px solid rgba(255, 255, 255, 0.1)',
      borderRadius: '12px',
      padding: '20px',
    },
    
    primaryText: {
      color: '#ffffff',
      fontWeight: 600,
    },
    
    secondaryText: {
      color: 'rgba(255, 255, 255, 0.8)',
      fontWeight: 400,
    },
    
    moduleCard: {
      background: 'rgba(255, 255, 255, 0.1)',
      backdropFilter: 'blur(20px)',
      WebkitBackdropFilter: 'blur(20px)',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      borderRadius: '16px',
      padding: '24px',
      color: '#ffffff',
      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
      cursor: 'pointer',
    },
    
    moduleHeader: {
      background: `linear-gradient(135deg, ${baseTheme.theme.colors.primary}20, ${baseTheme.theme.colors.primaryLight}10)`,
      backdropFilter: 'blur(10px)',
      WebkitBackdropFilter: 'blur(10px)',
      border: `1px solid ${baseTheme.theme.colors.primary}30`,
      borderRadius: '12px',
      padding: '16px',
      marginBottom: '24px',
      color: '#ffffff',
    },
  };
  
  // Helper functions for dynamic styling
  const getModuleColor = (module: string): string => {
    const moduleColors: Record<string, string> = {
      animals: baseTheme.theme.colors.primary,
      health: '#E53E3E',
      feeding: '#38A169',
      financial: '#3182CE',
      breeding: '#805AD5',
      inventory: '#D69E2E',
      commercial: '#00B5D8',
      reports: '#DD6B20',
      resources: '#319795',
      settings: '#718096',
      compliance: '#9F7AEA',
      dashboard: baseTheme.theme.colors.primary,
    };
    
    return moduleColors[module.toLowerCase()] || baseTheme.theme.colors.primary;
  };
  
  const getModuleGradient = (module: string): string => {
    const color = getModuleColor(module);
    return `linear-gradient(135deg, ${color} 0%, ${color}CC 50%, ${color}99 100%)`;
  };
  
  const applyHoverEffect = (baseStyle: React.CSSProperties): React.CSSProperties => ({
    ...baseStyle,
    '&:hover': styles.cardHover,
  });
  
  return {
    ...baseTheme,
    styles,
    getModuleColor,
    getModuleGradient,
    applyHoverEffect,
    
    // Utility functions
    createCardStyle: (customProps?: React.CSSProperties) => ({
      ...styles.card,
      ...customProps,
    }),
    
    createButtonStyle: (variant: 'primary' | 'secondary' | 'outlined' = 'primary', customProps?: React.CSSProperties) => ({
      ...styles[`${variant}Button`],
      ...customProps,
    }),
    
    createModuleStyle: (module: string, customProps?: React.CSSProperties) => ({
      ...styles.moduleCard,
      borderColor: `${getModuleColor(module)}50`,
      background: `linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, ${getModuleColor(module)}20 100%)`,
      ...customProps,
    }),
  };
};

export default useAgriIntelTheme;
