/**
 * Comprehensive BETA Test Suite
 * Complete validation for LIVE deployment readiness
 */

export interface ComprehensiveTestResult {
  loginInterface: {
    consolidated: boolean;
    visualDesign: boolean;
    backgroundVisible: boolean;
    themeApplied: boolean;
    issues: string[];
  };
  apiEndpoints: {
    animals: boolean;
    health: boolean;
    feeding: boolean;
    financial: boolean;
    settings: boolean;
    totalWorking: number;
    issues: string[];
  };
  dataPopulation: {
    animalsCount: number;
    healthCount: number;
    feedingCount: number;
    financialCount: number;
    relationshipsValid: boolean;
    issues: string[];
  };
  dashboardFunctionality: {
    modulesAccessible: boolean;
    formsWorking: boolean;
    navigationSmooth: boolean;
    interactiveElements: boolean;
    issues: string[];
  };
  betaCompliance: {
    limitEnforced: boolean;
    upgradePrompts: boolean;
    betaBadges: boolean;
    restrictedModulesBlocked: boolean;
    issues: string[];
  };
  productionReadiness: {
    criticalIssues: number;
    highIssues: number;
    readyForLive: boolean;
    blockers: string[];
  };
  overall: {
    passedTests: number;
    totalTests: number;
    successRate: number;
    readyForProduction: boolean;
  };
}

/**
 * Test login interface consolidation
 */
async function testLoginInterface(): Promise<any> {
  const result = {
    consolidated: false,
    visualDesign: false,
    backgroundVisible: false,
    themeApplied: false,
    issues: [] as string[]
  };

  try {
    // Test if we can access the primary login
    const loginResponse = await fetch('/login?tier=beta');
    result.consolidated = loginResponse.ok;

    // Check visual elements (if on login page)
    if (window.location.pathname.includes('login')) {
      const backgroundElements = document.querySelectorAll('[style*="background-image"]');
      result.backgroundVisible = backgroundElements.length > 0;

      const betaThemeElements = document.querySelectorAll('[style*="#FF9800"], .beta-theme, .beta-login');
      result.themeApplied = betaThemeElements.length > 0;

      const splitScreenLayout = document.querySelector('.split-screen-login, .login-image-panel');
      result.visualDesign = splitScreenLayout !== null;

      if (!result.backgroundVisible) result.issues.push('Background images not visible');
      if (!result.themeApplied) result.issues.push('BETA theme not applied');
      if (!result.visualDesign) result.issues.push('Split-screen design not implemented');
    }

  } catch (error) {
    result.issues.push(`Login test error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  return result;
}

/**
 * Test API endpoints
 */
async function testApiEndpoints(): Promise<any> {
  const result = {
    animals: false,
    health: false,
    feeding: false,
    financial: false,
    settings: false,
    totalWorking: 0,
    issues: [] as string[]
  };

  const endpoints = [
    { name: 'animals', url: '/api/animals' },
    { name: 'health', url: '/api/health/records' },
    { name: 'feeding', url: '/api/feeding/records' },
    { name: 'financial', url: '/api/financial/records' },
    { name: 'settings', url: '/api/settings' }
  ];

  for (const endpoint of endpoints) {
    try {
      const response = await fetch(`http://localhost:3002${endpoint.url}`);
      const success = response.ok;
      (result as any)[endpoint.name] = success;
      
      if (success) {
        result.totalWorking++;
      } else {
        result.issues.push(`${endpoint.name} API failed: ${response.status}`);
      }
    } catch (error) {
      result.issues.push(`${endpoint.name} API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  return result;
}

/**
 * Test data population
 */
async function testDataPopulation(): Promise<any> {
  const result = {
    animalsCount: 0,
    healthCount: 0,
    feedingCount: 0,
    financialCount: 0,
    relationshipsValid: false,
    issues: [] as string[]
  };

  try {
    // Test animals data
    const animalsResponse = await fetch('http://localhost:3002/api/animals');
    if (animalsResponse.ok) {
      const animalsData = await animalsResponse.json();
      const animals = animalsData.animals || animalsData;
      const betaAnimals = Array.isArray(animals) ? animals.filter((a: any) => 
        a.tagNumber?.startsWith('BETA-') || a.name === 'Tshepiso' || a.name === 'Lerato'
      ) : [];
      result.animalsCount = betaAnimals.length;
    }

    // Test health data
    const healthResponse = await fetch('http://localhost:3002/api/health/records');
    if (healthResponse.ok) {
      const healthData = await healthResponse.json();
      const healthRecords = healthData.records || healthData;
      result.healthCount = Array.isArray(healthRecords) ? healthRecords.length : 0;
    }

    // Test feeding data
    const feedingResponse = await fetch('http://localhost:3002/api/feeding/records');
    if (feedingResponse.ok) {
      const feedingData = await feedingResponse.json();
      const feedingRecords = feedingData.records || feedingData;
      result.feedingCount = Array.isArray(feedingRecords) ? feedingRecords.length : 0;
    }

    // Test financial data
    const financialResponse = await fetch('http://localhost:3002/api/financial/records');
    if (financialResponse.ok) {
      const financialData = await financialResponse.json();
      const financialRecords = financialData.records || financialData;
      result.financialCount = Array.isArray(financialRecords) ? financialRecords.length : 0;
    }

    // Validate relationships
    result.relationshipsValid = result.animalsCount >= 2 && result.healthCount >= 2 && 
                               result.feedingCount >= 2 && result.financialCount >= 2;

    // Check for issues
    if (result.animalsCount < 2) result.issues.push(`Only ${result.animalsCount} BETA animals found (expected 2)`);
    if (result.healthCount < 2) result.issues.push(`Only ${result.healthCount} health records found (expected 2)`);
    if (result.feedingCount < 2) result.issues.push(`Only ${result.feedingCount} feeding records found (expected 2)`);
    if (result.financialCount < 2) result.issues.push(`Only ${result.financialCount} financial records found (expected 2)`);

  } catch (error) {
    result.issues.push(`Data population test error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  return result;
}

/**
 * Test dashboard functionality
 */
function testDashboardFunctionality(): any {
  const result = {
    modulesAccessible: false,
    formsWorking: false,
    navigationSmooth: false,
    interactiveElements: false,
    issues: [] as string[]
  };

  try {
    // Check modules
    const moduleElements = document.querySelectorAll('[data-module], .module-card, .module-container');
    result.modulesAccessible = moduleElements.length >= 5;

    // Check forms
    const formElements = document.querySelectorAll('form, .MuiTextField-root, input, select');
    result.formsWorking = formElements.length > 0;

    // Check navigation
    const navElements = document.querySelectorAll('nav, .navigation, .sidebar, .menu, [role="navigation"]');
    result.navigationSmooth = navElements.length > 0;

    // Check interactive elements
    const buttons = document.querySelectorAll('button:not([disabled])');
    const links = document.querySelectorAll('a[href]');
    result.interactiveElements = buttons.length > 0 && links.length > 0;

    // Collect issues
    if (!result.modulesAccessible) result.issues.push('Modules not accessible');
    if (!result.formsWorking) result.issues.push('Forms not working');
    if (!result.navigationSmooth) result.issues.push('Navigation issues');
    if (!result.interactiveElements) result.issues.push('Interactive elements not working');

  } catch (error) {
    result.issues.push(`Dashboard test error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  return result;
}

/**
 * Test BETA compliance
 */
function testBetaCompliance(): any {
  const result = {
    limitEnforced: false,
    upgradePrompts: false,
    betaBadges: false,
    restrictedModulesBlocked: false,
    issues: [] as string[]
  };

  try {
    // Check limit enforcement
    const limitElements = document.querySelectorAll('[data-limit], .limit-indicator, .usage-progress');
    result.limitEnforced = limitElements.length > 0;

    // Check upgrade prompts
    const upgradeElements = document.querySelectorAll('[data-upgrade], .upgrade-prompt, .upgrade-button');
    result.upgradePrompts = upgradeElements.length > 0;

    // Check BETA badges
    const betaBadges = document.querySelectorAll('.beta-badge, [data-tier="beta"], .beta-chip');
    result.betaBadges = betaBadges.length > 0;

    // Check restricted modules
    const restrictedElements = document.querySelectorAll('[data-restricted], .locked-module, .premium-only');
    result.restrictedModulesBlocked = restrictedElements.length > 0;

    // Collect issues
    if (!result.limitEnforced) result.issues.push('Animal limit not enforced');
    if (!result.upgradePrompts) result.issues.push('No upgrade prompts found');
    if (!result.betaBadges) result.issues.push('No BETA badges visible');

  } catch (error) {
    result.issues.push(`BETA compliance test error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  return result;
}

/**
 * Run comprehensive BETA test suite
 */
export async function runComprehensiveBetaTest(): Promise<ComprehensiveTestResult> {
  console.log('🚀 Starting Comprehensive BETA Test Suite...');

  const loginInterface = await testLoginInterface();
  const apiEndpoints = await testApiEndpoints();
  const dataPopulation = await testDataPopulation();
  const dashboardFunctionality = testDashboardFunctionality();
  const betaCompliance = testBetaCompliance();

  // Calculate production readiness
  const criticalIssues = [
    ...dataPopulation.issues.filter(i => i.includes('expected 2')),
    ...apiEndpoints.issues.filter(i => i.includes('failed'))
  ].length;

  const highIssues = [
    ...loginInterface.issues,
    ...dashboardFunctionality.issues,
    ...betaCompliance.issues
  ].length;

  const productionReadiness = {
    criticalIssues,
    highIssues,
    readyForLive: criticalIssues === 0 && highIssues === 0,
    blockers: criticalIssues > 0 ? ['Critical data or API issues found'] : []
  };

  // Calculate overall success
  const tests = [
    loginInterface.consolidated && loginInterface.visualDesign,
    apiEndpoints.totalWorking >= 4,
    dataPopulation.relationshipsValid,
    dashboardFunctionality.modulesAccessible && dashboardFunctionality.formsWorking,
    betaCompliance.limitEnforced && betaCompliance.betaBadges
  ];

  const passedTests = tests.filter(Boolean).length;
  const totalTests = tests.length;

  const result: ComprehensiveTestResult = {
    loginInterface,
    apiEndpoints,
    dataPopulation,
    dashboardFunctionality,
    betaCompliance,
    productionReadiness,
    overall: {
      passedTests,
      totalTests,
      successRate: Math.round((passedTests / totalTests) * 100),
      readyForProduction: productionReadiness.readyForLive
    }
  };

  console.log('✅ Comprehensive BETA Test Complete:', result.overall);
  return result;
}

// Export for global access
(window as any).comprehensiveBetaTest = {
  runComprehensiveBetaTest
};
