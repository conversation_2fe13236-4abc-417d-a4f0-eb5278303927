/**
 * BETA API Comprehensive Audit
 * Tests all BETA-accessible endpoints and verifies data population
 */

export interface ApiTestResult {
  endpoint: string;
  method: string;
  status: number;
  success: boolean;
  dataCount: number;
  message: string;
  sampleData?: any;
}

export interface BetaApiAudit {
  animals: ApiTestResult;
  health: ApiTestResult;
  feeding: ApiTestResult;
  financial: ApiTestResult;
  settings: ApiTestResult;
  summary: {
    totalEndpoints: number;
    successfulEndpoints: number;
    failedEndpoints: number;
    successRate: number;
    totalRecords: number;
  };
}

const API_BASE_URL = 'http://localhost:3002/api';

/**
 * Test API endpoint
 */
async function testEndpoint(endpoint: string, method: string = 'GET'): Promise<ApiTestResult> {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();
    
    // Determine data count based on response structure
    let dataCount = 0;
    let sampleData = null;

    if (data.animals && Array.isArray(data.animals)) {
      dataCount = data.animals.length;
      sampleData = data.animals.slice(0, 2);
    } else if (data.records && Array.isArray(data.records)) {
      dataCount = data.records.length;
      sampleData = data.records.slice(0, 2);
    } else if (Array.isArray(data)) {
      dataCount = data.length;
      sampleData = data.slice(0, 2);
    } else if (data.data && Array.isArray(data.data)) {
      dataCount = data.data.length;
      sampleData = data.data.slice(0, 2);
    }

    return {
      endpoint,
      method,
      status: response.status,
      success: response.ok,
      dataCount,
      message: response.ok ? `Success - ${dataCount} records found` : `Failed - ${response.statusText}`,
      sampleData
    };

  } catch (error) {
    return {
      endpoint,
      method,
      status: 0,
      success: false,
      dataCount: 0,
      message: `Network Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}

/**
 * Run comprehensive BETA API audit
 */
export async function runBetaApiAudit(): Promise<BetaApiAudit> {
  console.log('🔍 Starting BETA API Comprehensive Audit...');

  // Test all BETA-accessible endpoints
  const animals = await testEndpoint('/animals');
  const health = await testEndpoint('/health/records');
  const feeding = await testEndpoint('/feeding/records');
  const financial = await testEndpoint('/financial/records');
  const settings = await testEndpoint('/settings');

  const allResults = [animals, health, feeding, financial, settings];
  const successfulEndpoints = allResults.filter(r => r.success).length;
  const totalRecords = allResults.reduce((sum, r) => sum + r.dataCount, 0);

  const audit: BetaApiAudit = {
    animals,
    health,
    feeding,
    financial,
    settings,
    summary: {
      totalEndpoints: allResults.length,
      successfulEndpoints,
      failedEndpoints: allResults.length - successfulEndpoints,
      successRate: Math.round((successfulEndpoints / allResults.length) * 100),
      totalRecords
    }
  };

  console.log('✅ BETA API Audit Complete:', audit.summary);
  return audit;
}

/**
 * Display detailed audit results
 */
export function displayBetaApiAudit(audit: BetaApiAudit) {
  console.group('📊 BETA API Audit Results');
  
  console.group('🐄 Animals API');
  console.log(`Status: ${audit.animals.success ? '✅' : '❌'} ${audit.animals.message}`);
  if (audit.animals.sampleData) {
    console.log('Sample Data:', audit.animals.sampleData);
  }
  console.groupEnd();

  console.group('🏥 Health API');
  console.log(`Status: ${audit.health.success ? '✅' : '❌'} ${audit.health.message}`);
  if (audit.health.sampleData) {
    console.log('Sample Data:', audit.health.sampleData);
  }
  console.groupEnd();

  console.group('🌾 Feeding API');
  console.log(`Status: ${audit.feeding.success ? '✅' : '❌'} ${audit.feeding.message}`);
  if (audit.feeding.sampleData) {
    console.log('Sample Data:', audit.feeding.sampleData);
  }
  console.groupEnd();

  console.group('💰 Financial API');
  console.log(`Status: ${audit.financial.success ? '✅' : '❌'} ${audit.financial.message}`);
  if (audit.financial.sampleData) {
    console.log('Sample Data:', audit.financial.sampleData);
  }
  console.groupEnd();

  console.group('⚙️ Settings API');
  console.log(`Status: ${audit.settings.success ? '✅' : '❌'} ${audit.settings.message}`);
  if (audit.settings.sampleData) {
    console.log('Sample Data:', audit.settings.sampleData);
  }
  console.groupEnd();

  console.group('📈 Summary');
  console.log(`Total Endpoints: ${audit.summary.totalEndpoints}`);
  console.log(`Successful: ${audit.summary.successfulEndpoints}`);
  console.log(`Failed: ${audit.summary.failedEndpoints}`);
  console.log(`Success Rate: ${audit.summary.successRate}%`);
  console.log(`Total Records: ${audit.summary.totalRecords}`);
  console.groupEnd();

  console.groupEnd();
}

/**
 * Test BETA data relationships
 */
export async function testBetaDataRelationships(): Promise<any> {
  console.log('🔗 Testing BETA Data Relationships...');

  try {
    const animalsResponse = await fetch(`${API_BASE_URL}/animals`);
    const animalsData = await animalsResponse.json();
    const animals = animalsData.animals || animalsData;

    const healthResponse = await fetch(`${API_BASE_URL}/health/records`);
    const healthData = await healthResponse.json();
    const healthRecords = healthData.records || healthData;

    const feedingResponse = await fetch(`${API_BASE_URL}/feeding/records`);
    const feedingData = await feedingResponse.json();
    const feedingRecords = feedingData.records || feedingData;

    const financialResponse = await fetch(`${API_BASE_URL}/financial/records`);
    const financialData = await financialResponse.json();
    const financialRecords = financialData.records || financialData;

    // Find BETA animals
    const betaAnimals = animals.filter((animal: any) => 
      animal.tagNumber?.startsWith('BETA-') || 
      animal.name === 'Tshepiso' || 
      animal.name === 'Lerato'
    );

    // Check relationships
    const animalIds = betaAnimals.map((a: any) => a._id || a.id);
    
    const linkedHealth = healthRecords.filter((h: any) => 
      animalIds.includes(h.animalId) || 
      betaAnimals.some((a: any) => a.name === h.animalName)
    );

    const linkedFeeding = feedingRecords.filter((f: any) => 
      animalIds.includes(f.animalId) || 
      betaAnimals.some((a: any) => a.name === f.animalName)
    );

    const linkedFinancial = financialRecords.filter((f: any) => 
      animalIds.includes(f.relatedAnimalId) || 
      betaAnimals.some((a: any) => a.name === f.relatedAnimalName)
    );

    const relationships = {
      betaAnimals: betaAnimals.length,
      linkedHealth: linkedHealth.length,
      linkedFeeding: linkedFeeding.length,
      linkedFinancial: linkedFinancial.length,
      healthCoverage: betaAnimals.length > 0 ? Math.round((linkedHealth.length / betaAnimals.length) * 100) : 0,
      feedingCoverage: betaAnimals.length > 0 ? Math.round((linkedFeeding.length / betaAnimals.length) * 100) : 0,
      financialCoverage: betaAnimals.length > 0 ? Math.round((linkedFinancial.length / betaAnimals.length) * 100) : 0,
    };

    console.log('🔗 Relationship Results:', relationships);
    return relationships;

  } catch (error) {
    console.error('❌ Relationship test failed:', error);
    return null;
  }
}

/**
 * Test CRUD operations
 */
export async function testBetaCrudOperations(): Promise<any> {
  console.log('🔧 Testing BETA CRUD Operations...');

  const results = {
    create: false,
    read: false,
    update: false,
    delete: false
  };

  try {
    // Test READ operation
    const readResponse = await fetch(`${API_BASE_URL}/animals`);
    results.read = readResponse.ok;

    // Test CREATE operation (mock data)
    const createResponse = await fetch(`${API_BASE_URL}/animals`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: 'Test Animal',
        species: 'Cattle',
        tagNumber: 'TEST-001',
        breed: 'Test Breed'
      })
    });
    results.create = createResponse.ok || createResponse.status === 400; // 400 might be validation error

    console.log('🔧 CRUD Test Results:', results);
    return results;

  } catch (error) {
    console.error('❌ CRUD test failed:', error);
    return results;
  }
}

// Export for global access
(window as any).betaApiAudit = {
  runBetaApiAudit,
  displayBetaApiAudit,
  testBetaDataRelationships,
  testBetaCrudOperations
};
