import React from 'react';
import { Box, Typography, Container, Card, CardContent } from '@mui/material';
import { 
  Security, 
  Verified, 
  Support, 
  CloudDone, 
  Shield,
  Assignment
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useLanguage } from '../../contexts/LanguageContext';

interface TrustSignal {
  id: string;
  icon: React.ReactNode;
  title: string;
  description: string;
  badge?: string;
}

const TrustSignalsSection: React.FC = () => {
  const { translate } = useLanguage();

  const trustSignals: TrustSignal[] = [
    {
      id: 'security',
      icon: <Security sx={{ fontSize: 40, color: '#4CAF50' }} />,
      title: 'Bank-Level Security',
      description: 'Your farm data is protected with 256-bit encryption and stored on secure South African servers.',
      badge: 'ISO 27001 Certified'
    },
    {
      id: 'compliance',
      icon: <Assignment sx={{ fontSize: 40, color: '#4CAF50' }} />,
      title: 'POPIA Compliant',
      description: 'Fully compliant with South African data protection laws and agricultural regulations.',
      badge: 'POPIA Certified'
    },
    {
      id: 'uptime',
      icon: <CloudDone sx={{ fontSize: 40, color: '#4CAF50' }} />,
      title: '99.9% Uptime',
      description: 'Reliable cloud infrastructure ensures your farm management system is always available.',
      badge: '24/7 Monitoring'
    },
    {
      id: 'support',
      icon: <Support sx={{ fontSize: 40, color: '#4CAF50' }} />,
      title: 'Local Support',
      description: 'South African-based support team that understands local farming practices and challenges.',
      badge: 'Expert Team'
    },
    {
      id: 'verified',
      icon: <Verified sx={{ fontSize: 40, color: '#4CAF50' }} />,
      title: 'Trusted by Farmers',
      description: 'Over 2,500 South African farmers trust AgriIntel to manage their livestock operations.',
      badge: '2,500+ Users'
    },
    {
      id: 'backup',
      icon: <Shield sx={{ fontSize: 40, color: '#4CAF50' }} />,
      title: 'Data Protection',
      description: 'Automated daily backups and disaster recovery ensure your farm records are never lost.',
      badge: 'Daily Backups'
    }
  ];

  const certifications = [
    {
      name: 'Department of Agriculture',
      logo: '/images/sponsors/Agriculture , land reform & rural development.png',
      description: 'Recognized by DALRRD'
    },
    {
      name: 'Agricultural Research Council',
      logo: '/images/sponsors/Agricultural research council.png',
      description: 'ARC Partnership'
    },
    {
      name: 'Land Bank',
      logo: '/images/sponsors/land bank.png',
      description: 'Financing Partner'
    },
    {
      name: 'NAMC',
      logo: '/images/sponsors/National Agricultural Marketing Council.png',
      description: 'Market Intelligence'
    }
  ];

  return (
    <Box
      component="section"
      sx={{
        py: { xs: 8, md: 12 },
        background: 'linear-gradient(135deg, #E8F5E8 0%, #F8F9FA 100%)',
        position: 'relative'
      }}
    >
      <Container maxWidth="lg">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <Box textAlign="center" mb={8}>
            <Typography
              variant="h2"
              component="h2"
              sx={{
                fontSize: { xs: '2rem', md: '2.5rem' },
                fontWeight: 700,
                color: '#2E7D32',
                mb: 2
              }}
            >
              {translate('landing.trust.title', { fallback: 'Trusted & Secure' })}
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: '#666',
                maxWidth: '600px',
                mx: 'auto',
                lineHeight: 1.6
              }}
            >
              {translate('landing.trust.subtitle', { 
                fallback: 'Your farm data is protected by enterprise-grade security and backed by industry certifications' 
              })}
            </Typography>
          </Box>
        </motion.div>

        {/* Trust Signals Grid */}
        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', lg: '1fr 1fr 1fr' },
            gap: 4,
            mb: 8
          }}
        >
          {trustSignals.map((signal, index) => (
            <motion.div
              key={signal.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card
                sx={{
                  height: '100%',
                  background: 'rgba(255, 255, 255, 0.9)',
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(46, 125, 50, 0.1)',
                  borderRadius: 3,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 12px 24px rgba(46, 125, 50, 0.1)'
                  }
                }}
              >
                <CardContent sx={{ p: 3, textAlign: 'center' }}>
                  {/* Icon */}
                  <Box mb={2}>
                    {signal.icon}
                  </Box>

                  {/* Badge */}
                  {signal.badge && (
                    <Box
                      sx={{
                        display: 'inline-block',
                        px: 2,
                        py: 0.5,
                        bgcolor: 'rgba(76, 175, 80, 0.1)',
                        color: '#2E7D32',
                        borderRadius: 20,
                        fontSize: '0.75rem',
                        fontWeight: 600,
                        mb: 2
                      }}
                    >
                      {signal.badge}
                    </Box>
                  )}

                  {/* Title */}
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 600,
                      color: '#2E7D32',
                      mb: 1,
                      fontSize: '1.1rem'
                    }}
                  >
                    {signal.title}
                  </Typography>

                  {/* Description */}
                  <Typography
                    variant="body2"
                    sx={{
                      color: '#666',
                      lineHeight: 1.6,
                      fontSize: '0.9rem'
                    }}
                  >
                    {signal.description}
                  </Typography>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </Box>

        {/* Certifications & Partners */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <Box
            sx={{
              textAlign: 'center',
              p: 4,
              background: 'rgba(255, 255, 255, 0.8)',
              borderRadius: 3,
              border: '1px solid rgba(46, 125, 50, 0.1)'
            }}
          >
            <Typography
              variant="h5"
              sx={{
                fontWeight: 600,
                color: '#2E7D32',
                mb: 4
              }}
            >
              Recognized & Supported By
            </Typography>

            <Box
              sx={{
                display: 'grid',
                gridTemplateColumns: { xs: '1fr 1fr', md: '1fr 1fr 1fr 1fr' },
                gap: 4,
                alignItems: 'center'
              }}
            >
              {certifications.map((cert, index) => (
                <Box
                  key={index}
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    gap: 1
                  }}
                >
                  <Box
                    component="img"
                    src={cert.logo}
                    alt={cert.name}
                    sx={{
                      height: 48,
                      width: 'auto',
                      objectFit: 'contain',
                      filter: 'grayscale(100%)',
                      opacity: 0.7,
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        filter: 'grayscale(0%)',
                        opacity: 1
                      }
                    }}
                  />
                  <Typography
                    variant="caption"
                    sx={{
                      color: '#666',
                      fontSize: '0.75rem',
                      textAlign: 'center'
                    }}
                  >
                    {cert.description}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Box>
        </motion.div>

        {/* Security Statement */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <Box
            sx={{
              textAlign: 'center',
              mt: 6,
              p: 3,
              background: 'linear-gradient(135deg, rgba(46, 125, 50, 0.05) 0%, rgba(76, 175, 80, 0.05) 100%)',
              borderRadius: 2,
              border: '1px solid rgba(46, 125, 50, 0.1)'
            }}
          >
            <Typography
              variant="body1"
              sx={{
                color: '#2E7D32',
                fontWeight: 500,
                fontSize: '1rem',
                lineHeight: 1.6
              }}
            >
              🔒 Your farm data is encrypted, backed up daily, and stored securely in South Africa. 
              We never share your information and you maintain full ownership of your data.
            </Typography>
          </Box>
        </motion.div>
      </Container>
    </Box>
  );
};

export default TrustSignalsSection;
