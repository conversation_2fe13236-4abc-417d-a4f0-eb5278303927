/*
 * AGRIINTEL PROFESSIONAL V2 STYLES
 * Green branding for professional tier
 * Full-featured SaaS design
 */

:root {
  /* Professional V2 Color Palette */
  --pro-primary: #4CAF50;
  --pro-primary-light: #66BB6A;
  --pro-primary-dark: #388E3C;
  --pro-secondary: #2E7D32;
  --pro-accent: #8BC34A;
  
  /* Professional Gradients */
  --pro-gradient-primary: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
  --pro-gradient-secondary: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
  --pro-gradient-accent: linear-gradient(135deg, #66BB6A 0%, #8BC34A 100%);
  
  /* Professional UI Colors */
  --pro-bg-light: rgba(76, 175, 80, 0.1);
  --pro-bg-medium: rgba(76, 175, 80, 0.2);
  --pro-border: rgba(76, 175, 80, 0.3);
  --pro-text-on-primary: #FFFFFF;
  --pro-text-secondary: #2E7D32;
  
  /* Success and Status Colors */
  --pro-success: #4CAF50;
  --pro-warning: #FF9800;
  --pro-error: #F44336;
  --pro-info: #2196F3;
}

/* Professional Login Page Styling */
.pro-login-container {
  background: var(--pro-gradient-primary);
  min-height: 100vh;
  position: relative;
}

.pro-login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('../../public/images/gallery/cattle-farm-1.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  opacity: 0.2;
  z-index: 0;
}

.pro-login-card {
  background: rgba(255, 255, 255, 1) !important;
  border: 2px solid var(--pro-border) !important;
  border-radius: 16px !important;
  box-shadow: 0 12px 40px rgba(76, 175, 80, 0.25) !important;
}

.pro-brand-badge {
  background: var(--pro-gradient-primary) !important;
  color: var(--pro-text-on-primary) !important;
  font-weight: 700 !important;
  font-size: 0.875rem !important;
  padding: 0.5rem 1rem !important;
  border-radius: 20px !important;
  margin-bottom: 1rem !important;
  display: inline-block;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.pro-login-title {
  color: var(--pro-primary-dark) !important;
  font-weight: 800 !important;
  margin-bottom: 0.5rem !important;
}

.pro-login-subtitle {
  color: var(--pro-text-secondary) !important;
  margin-bottom: 2rem !important;
}

.pro-login-button {
  background: var(--pro-gradient-primary) !important;
  color: var(--pro-text-on-primary) !important;
  font-weight: 600 !important;
  border-radius: 8px !important;
  padding: 12px 24px !important;
  text-transform: none !important;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3) !important;
  transition: all 0.3s ease !important;
}

.pro-login-button:hover {
  background: var(--pro-gradient-secondary) !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4) !important;
}

/* Professional Dashboard Styling */
.pro-dashboard-header {
  background: var(--pro-gradient-primary) !important;
  color: var(--pro-text-on-primary) !important;
  padding: 1rem 2rem !important;
  border-radius: 0 0 16px 16px !important;
  margin-bottom: 2rem !important;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2) !important;
}

.pro-dashboard-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.pro-tier-badge {
  background: rgba(255, 255, 255, 0.2) !important;
  color: var(--pro-text-on-primary) !important;
  font-weight: 700 !important;
  font-size: 0.75rem !important;
  padding: 0.25rem 0.75rem !important;
  border-radius: 12px !important;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Professional Sidebar Styling */
.pro-sidebar {
  background: var(--pro-gradient-secondary) !important;
}

.pro-sidebar .MuiListItemButton-root {
  color: var(--pro-text-on-primary) !important;
}

.pro-sidebar .MuiListItemButton-root:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

.pro-sidebar .MuiListItemButton-root.Mui-selected {
  background: rgba(255, 255, 255, 0.2) !important;
}

/* Professional Module Cards */
.pro-module-card {
  border: 2px solid var(--pro-border) !important;
  border-radius: 12px !important;
  background: var(--pro-bg-light) !important;
  transition: all 0.3s ease !important;
}

.pro-module-card:hover {
  border-color: var(--pro-primary) !important;
  background: var(--pro-bg-medium) !important;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(76, 175, 80, 0.2) !important;
}

.pro-module-icon {
  color: var(--pro-primary) !important;
  font-size: 3rem !important;
  margin-bottom: 1rem !important;
}

.pro-module-title {
  color: var(--pro-primary-dark) !important;
  font-weight: 700 !important;
  margin-bottom: 0.5rem !important;
}

.pro-module-description {
  color: var(--pro-text-secondary) !important;
  font-size: 0.875rem !important;
}

/* Professional Feature Highlights */
.pro-feature-highlight {
  background: var(--pro-bg-light) !important;
  border: 2px solid var(--pro-primary) !important;
  border-radius: 12px !important;
  padding: 1.5rem !important;
  margin: 1rem 0 !important;
  position: relative;
  overflow: hidden;
}

.pro-feature-highlight::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--pro-gradient-primary);
}

.pro-feature-title {
  color: var(--pro-primary-dark) !important;
  font-weight: 700 !important;
  font-size: 1.125rem !important;
  margin-bottom: 0.5rem !important;
}

.pro-feature-description {
  color: var(--pro-text-secondary) !important;
  margin-bottom: 1rem !important;
  font-size: 0.875rem !important;
}

/* Professional Metrics and Stats */
.pro-metric-card {
  background: var(--pro-bg-light) !important;
  border: 1px solid var(--pro-border) !important;
  border-radius: 8px !important;
  padding: 1rem !important;
  position: relative;
  overflow: hidden;
}

.pro-metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: var(--pro-gradient-primary);
}

.pro-metric-value {
  color: var(--pro-primary-dark) !important;
  font-weight: 700 !important;
  font-size: 1.5rem !important;
}

.pro-metric-label {
  color: var(--pro-text-secondary) !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
}

.pro-metric-trend {
  color: var(--pro-success) !important;
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Professional Action Buttons */
.pro-action-button {
  background: var(--pro-gradient-primary) !important;
  color: var(--pro-text-on-primary) !important;
  font-weight: 600 !important;
  padding: 0.75rem 1.5rem !important;
  border-radius: 8px !important;
  text-transform: none !important;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3) !important;
  transition: all 0.3s ease !important;
}

.pro-action-button:hover {
  background: var(--pro-gradient-secondary) !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4) !important;
}

.pro-action-button-secondary {
  background: transparent !important;
  color: var(--pro-primary) !important;
  border: 2px solid var(--pro-primary) !important;
  font-weight: 600 !important;
  padding: 0.75rem 1.5rem !important;
  border-radius: 8px !important;
  text-transform: none !important;
  transition: all 0.3s ease !important;
}

.pro-action-button-secondary:hover {
  background: var(--pro-primary) !important;
  color: var(--pro-text-on-primary) !important;
  transform: translateY(-2px);
}

/* Professional Data Tables */
.pro-data-table {
  background: white !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1) !important;
  overflow: hidden;
}

.pro-data-table .MuiTableHead-root {
  background: var(--pro-gradient-primary) !important;
}

.pro-data-table .MuiTableHead-root .MuiTableCell-root {
  color: var(--pro-text-on-primary) !important;
  font-weight: 600 !important;
}

.pro-data-table .MuiTableRow-root:hover {
  background: var(--pro-bg-light) !important;
}

/* Professional Charts and Analytics */
.pro-chart-container {
  background: white !important;
  border-radius: 12px !important;
  padding: 1.5rem !important;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.1) !important;
  border: 1px solid var(--pro-border) !important;
}

.pro-chart-title {
  color: var(--pro-primary-dark) !important;
  font-weight: 700 !important;
  margin-bottom: 1rem !important;
}

/* Professional Responsive Design */
@media (max-width: 768px) {
  .pro-dashboard-header {
    padding: 1rem !important;
    margin-bottom: 1rem !important;
  }
  
  .pro-feature-highlight {
    padding: 1rem !important;
    margin: 0.5rem 0 !important;
  }
  
  .pro-module-card {
    margin-bottom: 1rem !important;
  }
}

/* Professional Animation Effects */
@keyframes pro-pulse {
  0% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(76, 175, 80, 0); }
  100% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0); }
}

.pro-pulse-animation {
  animation: pro-pulse 2s infinite;
}

/* Professional Focus States for Accessibility */
.pro-login-container *:focus,
.pro-dashboard-header *:focus,
.pro-module-card *:focus {
  outline: 2px solid var(--pro-primary) !important;
  outline-offset: 2px;
}

/* High Contrast Mode Support for Professional */
@media (prefers-contrast: high) {
  :root {
    --pro-primary: #2E7D32;
    --pro-primary-light: #4CAF50;
    --pro-primary-dark: #1B5E20;
  }
}

/* Reduced Motion Support for Professional */
@media (prefers-reduced-motion: reduce) {
  .pro-module-card,
  .pro-login-button,
  .pro-action-button {
    transition: none !important;
  }
  
  .pro-pulse-animation {
    animation: none !important;
  }
}
