/* Enhanced Theme Selector Styles */

.agri-theme-selector {
  padding: 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  max-width: 800px;
  margin: 0 auto;
}

.theme-selector-header {
  text-align: center;
  margin-bottom: 2rem;
}

.theme-selector-header h3 {
  font-size: 2rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #1565C0, #2E7D32, #F57C00);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.theme-selector-header p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

/* Theme Categories */
.theme-categories {
  margin-bottom: 2rem;
}

.theme-category {
  margin-bottom: 2rem;
}

.category-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

.theme-variants-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

/* Theme Variant Cards */
.theme-variant-card {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.theme-variant-card:hover {
  transform: translateY(-4px) scale(1.02);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.theme-variant-card.active {
  border-color: #4CAF50;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.3);
  background: rgba(76, 175, 80, 0.1);
}

.theme-variant-card.active::before {
  content: '✓';
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: #4CAF50;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
}

/* Theme Preview */
.theme-preview {
  width: 100%;
  height: 60px;
  border-radius: 12px;
  margin-bottom: 1rem;
  position: relative;
  overflow: hidden;
}

.theme-preview::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Theme Info */
.theme-info {
  text-align: center;
}

.theme-name {
  display: block;
  font-size: 1rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 0.5rem;
}

.theme-colors {
  display: flex;
  justify-content: center;
  gap: 0.3rem;
}

.color-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Theme Mode Section */
.theme-mode-section {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 1.5rem;
}

.theme-mode-section h4 {
  font-size: 1.2rem;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 1rem;
  text-align: center;
}

.theme-modes {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.theme-mode-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 0.8rem 1.5rem;
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  min-width: 100px;
}

.theme-mode-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
}

.theme-mode-btn.active {
  background: linear-gradient(135deg, #1565C0, #2E7D32);
  border-color: #4CAF50;
  color: white;
  box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .agri-theme-selector {
    padding: 1.5rem;
    margin: 1rem;
  }
  
  .theme-variants-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.8rem;
  }
  
  .theme-variant-card {
    padding: 0.8rem;
  }
  
  .theme-preview {
    height: 50px;
  }
  
  .theme-modes {
    flex-direction: column;
    align-items: center;
  }
  
  .theme-mode-btn {
    width: 100%;
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .theme-selector-header h3 {
    font-size: 1.5rem;
  }
  
  .theme-variants-grid {
    grid-template-columns: 1fr;
  }
  
  .category-title {
    font-size: 1.1rem;
  }
}
