/**
 * Final LIVE Deployment Verification
 * Comprehensive check for production readiness
 */

export interface FinalDeploymentStatus {
  syntaxErrors: {
    fixed: boolean;
    details: string;
  };
  apiEndpoints: {
    allWorking: boolean;
    workingCount: number;
    totalCount: number;
    details: string[];
  };
  visualDesign: {
    themeConsistent: boolean;
    glassmorphismApplied: boolean;
    backgroundsVisible: boolean;
    noWhiteBackgrounds: boolean;
    details: string[];
  };
  inlineStyles: {
    cleaned: boolean;
    remainingCount: number;
    details: string[];
  };
  betaCompliance: {
    limitEnforced: boolean;
    upgradePrompts: boolean;
    betaBadges: boolean;
    details: string[];
  };
  dataIntegrity: {
    animalsPopulated: boolean;
    healthRecords: boolean;
    feedingRecords: boolean;
    financialRecords: boolean;
    relationshipsValid: boolean;
    details: string[];
  };
  overall: {
    readyForLive: boolean;
    successRate: number;
    criticalIssues: string[];
    recommendations: string[];
  };
}

const API_BASE_URL = 'http://localhost:3002/api';

/**
 * Check syntax errors (compilation)
 */
function checkSyntaxErrors(): any {
  // Check if the application is running without compilation errors
  const hasErrors = document.querySelector('.error-overlay, .compilation-error');
  
  return {
    fixed: !hasErrors,
    details: hasErrors ? 'Compilation errors detected' : 'No syntax errors found'
  };
}

/**
 * Verify API endpoints
 */
async function verifyApiEndpoints(): Promise<any> {
  const endpoints = [
    { name: 'animals', url: '/animals' },
    { name: 'health', url: '/health/records' },
    { name: 'feeding', url: '/feeding/records' },
    { name: 'financial', url: '/financial/records' },
    { name: 'settings', url: '/settings' }
  ];

  const details: string[] = [];
  let workingCount = 0;

  for (const endpoint of endpoints) {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint.url}`);
      if (response.ok) {
        workingCount++;
        details.push(`✅ ${endpoint.name}: Working`);
      } else {
        details.push(`❌ ${endpoint.name}: Failed (${response.status})`);
      }
    } catch (error) {
      details.push(`❌ ${endpoint.name}: Error - ${error instanceof Error ? error.message : 'Unknown'}`);
    }
  }

  return {
    allWorking: workingCount === endpoints.length,
    workingCount,
    totalCount: endpoints.length,
    details
  };
}

/**
 * Check visual design consistency
 */
function checkVisualDesign(): any {
  const details: string[] = [];

  // Check for AgriIntel theme colors
  const agriIntelColors = ['#1565C0', '#2E7D32', '#F57C00'];
  const elementsWithTheme = Array.from(document.querySelectorAll('*')).filter(el => {
    const styles = window.getComputedStyle(el);
    return agriIntelColors.some(color => 
      styles.color.includes(color) || 
      styles.backgroundColor.includes(color) || 
      styles.borderColor.includes(color)
    );
  });
  const themeConsistent = elementsWithTheme.length > 0;

  // Check for glassmorphism effects
  const glassmorphElements = document.querySelectorAll('[style*="backdrop-filter"], [style*="backdropFilter"], .glass-base, .glass-card');
  const glassmorphismApplied = glassmorphElements.length > 0;

  // Check for background images
  const backgroundElements = document.querySelectorAll('[style*="background-image"], .agriintel-tabpanel');
  const backgroundsVisible = backgroundElements.length > 0;

  // Check for problematic white backgrounds
  const whiteBackgrounds = document.querySelectorAll('[style*="background: #FFFFFF"], [style*="background-color: white"]');
  const noWhiteBackgrounds = whiteBackgrounds.length === 0;

  if (themeConsistent) details.push('✅ AgriIntel theme colors applied');
  else details.push('❌ AgriIntel theme colors missing');

  if (glassmorphismApplied) details.push('✅ Glassmorphism effects present');
  else details.push('❌ Glassmorphism effects missing');

  if (backgroundsVisible) details.push('✅ Background images visible');
  else details.push('❌ Background images not visible');

  if (noWhiteBackgrounds) details.push('✅ No white backgrounds covering design');
  else details.push(`❌ ${whiteBackgrounds.length} white backgrounds found`);

  return {
    themeConsistent,
    glassmorphismApplied,
    backgroundsVisible,
    noWhiteBackgrounds,
    details
  };
}

/**
 * Check inline styles cleanup
 */
function checkInlineStyles(): any {
  const inlineStyleElements = document.querySelectorAll('[style]');
  const details: string[] = [];

  // Filter out acceptable inline styles (like dynamic positioning)
  const problematicInlineStyles = Array.from(inlineStyleElements).filter(el => {
    const style = el.getAttribute('style') || '';
    return style.includes('background:') || style.includes('color:') || style.includes('font-');
  });

  const cleaned = problematicInlineStyles.length === 0;
  const remainingCount = problematicInlineStyles.length;

  if (cleaned) {
    details.push('✅ All problematic inline styles moved to CSS');
  } else {
    details.push(`❌ ${remainingCount} elements still have problematic inline styles`);
    problematicInlineStyles.slice(0, 5).forEach((el, index) => {
      details.push(`  - Element ${index + 1}: ${el.tagName} with style="${el.getAttribute('style')?.slice(0, 50)}..."`);
    });
  }

  return {
    cleaned,
    remainingCount,
    details
  };
}

/**
 * Check BETA compliance
 */
function checkBetaCompliance(): any {
  const details: string[] = [];

  // Check for limit enforcement
  const limitElements = document.querySelectorAll('[data-limit], .limit-indicator, .usage-progress');
  const limitEnforced = limitElements.length > 0;

  // Check for upgrade prompts
  const upgradeElements = document.querySelectorAll('[data-upgrade], .upgrade-prompt, .upgrade-button');
  const upgradePrompts = upgradeElements.length > 0;

  // Check for BETA badges
  const betaBadges = document.querySelectorAll('.beta-badge, [data-tier="beta"], .beta-chip');
  const betaBadgesPresent = betaBadges.length > 0;

  if (limitEnforced) details.push('✅ Animal limit enforcement present');
  else details.push('❌ Animal limit enforcement missing');

  if (upgradePrompts) details.push('✅ Upgrade prompts found');
  else details.push('❌ Upgrade prompts missing');

  if (betaBadgesPresent) details.push('✅ BETA badges visible');
  else details.push('❌ BETA badges missing');

  return {
    limitEnforced,
    upgradePrompts,
    betaBadges: betaBadgesPresent,
    details
  };
}

/**
 * Check data integrity
 */
async function checkDataIntegrity(): Promise<any> {
  const details: string[] = [];

  try {
    // Check animals
    const animalsResponse = await fetch(`${API_BASE_URL}/animals`);
    let animalsPopulated = false;
    if (animalsResponse.ok) {
      const animalsData = await animalsResponse.json();
      const animals = animalsData.animals || animalsData;
      const betaAnimals = Array.isArray(animals) ? animals.filter((a: any) => 
        a.tagNumber?.startsWith('BETA-') || a.name === 'Tshepiso' || a.name === 'Lerato'
      ) : [];
      animalsPopulated = betaAnimals.length >= 2;
      details.push(animalsPopulated ? `✅ ${betaAnimals.length} BETA animals found` : `❌ Only ${betaAnimals.length} BETA animals found`);
    }

    // Check health records
    const healthResponse = await fetch(`${API_BASE_URL}/health/records`);
    let healthRecords = false;
    if (healthResponse.ok) {
      const healthData = await healthResponse.json();
      const records = healthData.records || healthData;
      const betaRecords = Array.isArray(records) ? records.filter((h: any) =>
        h.animalName === 'Tshepiso' || h.animalName === 'Lerato'
      ) : [];
      healthRecords = betaRecords.length >= 2;
      details.push(healthRecords ? `✅ ${betaRecords.length} health records found` : `❌ Only ${betaRecords.length} health records found`);
    }

    // Check feeding records
    const feedingResponse = await fetch(`${API_BASE_URL}/feeding/records`);
    let feedingRecords = false;
    if (feedingResponse.ok) {
      const feedingData = await feedingResponse.json();
      const records = feedingData.records || feedingData;
      const betaRecords = Array.isArray(records) ? records.filter((f: any) =>
        f.animalName === 'Tshepiso' || f.animalName === 'Lerato'
      ) : [];
      feedingRecords = betaRecords.length >= 2;
      details.push(feedingRecords ? `✅ ${betaRecords.length} feeding records found` : `❌ Only ${betaRecords.length} feeding records found`);
    }

    // Check financial records
    const financialResponse = await fetch(`${API_BASE_URL}/financial/records`);
    let financialRecords = false;
    if (financialResponse.ok) {
      const financialData = await financialResponse.json();
      const records = financialData.records || financialData;
      const betaRecords = Array.isArray(records) ? records.filter((f: any) =>
        f.relatedAnimalName === 'Tshepiso' || f.relatedAnimalName === 'Lerato'
      ) : [];
      financialRecords = betaRecords.length >= 2;
      details.push(financialRecords ? `✅ ${betaRecords.length} financial records found` : `❌ Only ${betaRecords.length} financial records found`);
    }

    const relationshipsValid = animalsPopulated && healthRecords && feedingRecords && financialRecords;

    return {
      animalsPopulated,
      healthRecords,
      feedingRecords,
      financialRecords,
      relationshipsValid,
      details
    };

  } catch (error) {
    details.push(`❌ Data integrity check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return {
      animalsPopulated: false,
      healthRecords: false,
      feedingRecords: false,
      financialRecords: false,
      relationshipsValid: false,
      details
    };
  }
}

/**
 * Run final LIVE deployment check
 */
export async function runFinalLiveDeploymentCheck(): Promise<FinalDeploymentStatus> {
  console.log('🚀 Running Final LIVE Deployment Check...');

  const syntaxErrors = checkSyntaxErrors();
  const apiEndpoints = await verifyApiEndpoints();
  const visualDesign = checkVisualDesign();
  const inlineStyles = checkInlineStyles();
  const betaCompliance = checkBetaCompliance();
  const dataIntegrity = await checkDataIntegrity();

  // Calculate overall status
  const criticalIssues: string[] = [];
  const recommendations: string[] = [];

  if (!syntaxErrors.fixed) criticalIssues.push('Syntax errors must be fixed');
  if (!apiEndpoints.allWorking) criticalIssues.push('All API endpoints must be working');
  if (!dataIntegrity.relationshipsValid) criticalIssues.push('Data relationships must be valid');

  if (!visualDesign.themeConsistent) recommendations.push('Apply AgriIntel theme consistently');
  if (!visualDesign.glassmorphismApplied) recommendations.push('Add glassmorphism effects');
  if (!inlineStyles.cleaned) recommendations.push('Move remaining inline styles to CSS');
  if (!betaCompliance.limitEnforced) recommendations.push('Implement BETA limit enforcement');

  const totalChecks = 6;
  const passedChecks = [
    syntaxErrors.fixed,
    apiEndpoints.allWorking,
    visualDesign.themeConsistent && visualDesign.glassmorphismApplied,
    inlineStyles.cleaned,
    betaCompliance.limitEnforced && betaCompliance.betaBadges,
    dataIntegrity.relationshipsValid
  ].filter(Boolean).length;

  const successRate = Math.round((passedChecks / totalChecks) * 100);
  const readyForLive = criticalIssues.length === 0;

  const result: FinalDeploymentStatus = {
    syntaxErrors,
    apiEndpoints,
    visualDesign,
    inlineStyles,
    betaCompliance,
    dataIntegrity,
    overall: {
      readyForLive,
      successRate,
      criticalIssues,
      recommendations
    }
  };

  console.log('✅ Final LIVE Deployment Check Complete');
  console.log(`Ready for LIVE: ${readyForLive ? 'YES' : 'NO'}`);
  console.log(`Success Rate: ${successRate}%`);

  return result;
}

// Export for global access
(window as any).finalLiveDeploymentCheck = {
  runFinalLiveDeploymentCheck
};
