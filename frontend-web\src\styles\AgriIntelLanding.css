/* General Body Styles */
body {
    font-family: 'Arial', sans-serif;
    margin: 0;
    color: #333;
    background: linear-gradient(135deg,
        rgba(46, 125, 50, 0.95) 0%,
        rgba(21, 101, 192, 0.90) 25%,
        rgba(46, 125, 50, 0.85) 50%,
        rgba(21, 101, 192, 0.90) 75%,
        rgba(46, 125, 50, 0.95) 100%);
}

/* Landing Page Container */
.landing-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Header and Navigation */
.landing-header {
    background: linear-gradient(135deg,
        rgba(46, 125, 50, 0.9) 0%,
        rgba(21, 101, 192, 0.85) 50%,
        rgba(46, 125, 50, 0.9) 100%);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    padding: 1rem 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.logo {
    font-size: 1.8rem;
    font-weight: bold;
    color: #2c3e50;
}

.nav-links a {
    margin: 0 15px;
    text-decoration: none;
    color: #2c3e50;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-links a:hover {
    color: #1abc9c;
}

/* Hero Section */
.hero-section {
    background: url('https://images.unsplash.com/photo-1560493676-04071c5f467b?q=80&w=1974&auto=format&fit=crop') no-repeat center center/cover;
    color: white;
    text-align: center;
    padding: 100px 20px;
    position: relative;
}

.hero-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto;
}

.hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 0.5rem;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

/* Call-to-Action Buttons */
.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.cta-button {
    padding: 12px 25px;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
}

.cta-button.register {
    background-color: #3498db; /* Blue */
    color: white;
}

.cta-button.beta {
    background-color: #1abc9c; /* Turquoise */
    color: white;
}

.cta-button.professional {
    background-color: #e67e22; /* Orange */
    color: white;
}

.cta-button.enterprise {
    background-color: #2c3e50; /* Dark Blue */
    color: white;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Content Sections */
.content-section {
    padding: 60px 20px;
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
}

.content-section h2 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;
}

.content-section h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background-color: #1abc9c;
}

.content-section p {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #555;
    max-width: 800px;
    margin: 1.5rem auto;
}

/* Footer */
.landing-footer {
    background: #2c3e50;
    color: white;
    text-align: center;
    padding: 2rem;
}

.landing-footer p {
    margin: 0;
}