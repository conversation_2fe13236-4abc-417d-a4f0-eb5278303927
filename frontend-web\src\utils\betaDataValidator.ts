/**
 * BETA Data Validator
 * Validates that BETA modules have proper data and relationships
 */

import { Animal } from '../types/animal';

export interface BetaValidationResult {
  isValid: boolean;
  issues: string[];
  recommendations: string[];
  dataCount: {
    animals: number;
    healthRecords: number;
    feedingRecords: number;
    financialRecords: number;
  };
}

export interface BetaSampleData {
  animals: Animal[];
  healthRecords: any[];
  feedingRecords: any[];
  financialRecords: any[];
}

// Create BETA sample data for testing
export const createBetaSampleData = (): BetaSampleData => {
  const now = new Date();
  const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  const twoWeeksAgo = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);

  const animals: Animal[] = [
    {
      id: 'beta_animal_001',
      tagNumber: 'BETA-001',
      name: 'Tshepiso',
      species: 'Cattle',
      type: 'Beef',
      breed: 'Nguni',
      gender: 'Female',
      birthDate: '2021-03-15',
      status: 'Active',
      location: 'Veld A - Limpopo',
      weight: 485,
      purchaseDate: '2021-04-20',
      purchasePrice: 18500,
      healthStatus: 'healthy',
      rfidTag: 'RFID_BETA_001',
      notes: 'Excellent breeding cow, adapted to local conditions',
      imageUrl: '/images/animals/cattle-1.jpeg',
      createdAt: oneMonthAgo,
      updatedAt: twoWeeksAgo
    },
    {
      id: 'beta_animal_002',
      tagNumber: 'BETA-002',
      name: 'Lerato',
      species: 'Cattle',
      type: 'Dairy',
      breed: 'Holstein',
      gender: 'Female',
      birthDate: '2020-08-22',
      status: 'Active',
      location: 'Paddock B - Gauteng',
      weight: 520,
      purchaseDate: '2020-10-15',
      purchasePrice: 22000,
      healthStatus: 'pregnant',
      rfidTag: 'RFID_BETA_002',
      notes: 'High milk producer, expecting calf in 2 months',
      imageUrl: '/images/animals/cattle-2.jpeg',
      createdAt: oneMonthAgo,
      updatedAt: twoWeeksAgo
    }
  ];

  const healthRecords = [
    {
      id: 'beta_health_001',
      animalId: 'beta_animal_001',
      animalName: 'Tshepiso',
      date: twoWeeksAgo,
      type: 'vaccination',
      description: 'Annual FMD and Blackleg vaccination',
      performedBy: 'Dr. Mthembu',
      cost: 285.00,
      notes: 'Routine vaccination completed successfully. No adverse reactions observed.',
      status: 'completed'
    },
    {
      id: 'beta_health_002',
      animalId: 'beta_animal_002',
      animalName: 'Lerato',
      date: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
      type: 'checkup',
      description: 'Pregnancy checkup and ultrasound',
      performedBy: 'Dr. Van Der Merwe',
      cost: 450.00,
      notes: 'Pregnancy progressing well. Calf development normal.',
      status: 'completed'
    }
  ];

  const feedingRecords = [
    {
      id: 'beta_feeding_001',
      animalId: 'beta_animal_001',
      animalName: 'Tshepiso',
      date: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000),
      feedType: 'Lucerne Hay',
      quantity: 12,
      unit: 'kg',
      location: 'Veld A - Limpopo',
      cost: 156.00,
      notes: 'High quality lucerne for breeding cow nutrition'
    },
    {
      id: 'beta_feeding_002',
      animalId: 'beta_animal_002',
      animalName: 'Lerato',
      date: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000),
      feedType: 'Dairy Concentrate',
      quantity: 8,
      unit: 'kg',
      location: 'Paddock B - Gauteng',
      cost: 224.00,
      notes: 'Special pregnancy feed with added calcium and vitamins'
    }
  ];

  const financialRecords = [
    {
      id: 'beta_financial_001',
      type: 'expense',
      category: 'Health Care',
      description: 'Vaccination for Tshepiso (BETA-001)',
      amount: 285.00,
      date: twoWeeksAgo,
      relatedAnimalId: 'beta_animal_001',
      relatedAnimalName: 'Tshepiso',
      relatedRecordId: 'beta_health_001'
    },
    {
      id: 'beta_financial_002',
      type: 'expense',
      category: 'Feed',
      description: 'Feed costs for Lerato and Tshepiso',
      amount: 380.00,
      date: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000),
      relatedAnimalId: 'beta_animal_002',
      relatedAnimalName: 'Lerato',
      relatedRecordId: 'beta_feeding_002'
    }
  ];

  return {
    animals,
    healthRecords,
    feedingRecords,
    financialRecords
  };
};

// Validate BETA data integrity
export const validateBetaData = (data: BetaSampleData): BetaValidationResult => {
  const issues: string[] = [];
  const recommendations: string[] = [];

  // Check if we have exactly 2 records per module
  if (data.animals.length !== 2) {
    issues.push(`Expected 2 animals, found ${data.animals.length}`);
  }
  if (data.healthRecords.length !== 2) {
    issues.push(`Expected 2 health records, found ${data.healthRecords.length}`);
  }
  if (data.feedingRecords.length !== 2) {
    issues.push(`Expected 2 feeding records, found ${data.feedingRecords.length}`);
  }
  if (data.financialRecords.length !== 2) {
    issues.push(`Expected 2 financial records, found ${data.financialRecords.length}`);
  }

  // Check data relationships
  const animalIds = data.animals.map(a => a.id);
  
  // Verify health records link to animals
  const healthLinked = data.healthRecords.filter(h => animalIds.includes(h.animalId));
  if (healthLinked.length !== data.healthRecords.length) {
    issues.push('Some health records are not linked to animals');
  }

  // Verify feeding records link to animals
  const feedingLinked = data.feedingRecords.filter(f => animalIds.includes(f.animalId));
  if (feedingLinked.length !== data.feedingRecords.length) {
    issues.push('Some feeding records are not linked to animals');
  }

  // Verify financial records link to animals or other records
  const financialLinked = data.financialRecords.filter(f => 
    animalIds.includes(f.relatedAnimalId) || 
    data.healthRecords.some(h => h.id === f.relatedRecordId) ||
    data.feedingRecords.some(fr => fr.id === f.relatedRecordId)
  );
  if (financialLinked.length !== data.financialRecords.length) {
    issues.push('Some financial records are not properly linked');
  }

  // Check for South African context
  const hasAfricanNames = data.animals.some(a => 
    ['Tshepiso', 'Lerato', 'Tumelo', 'Naledi', 'Botha', 'Venter'].includes(a.name)
  );
  if (!hasAfricanNames) {
    recommendations.push('Consider using South African names (Sotho, Tswana, Afrikaans)');
  }

  // Check for realistic data
  const hasRealisticPrices = data.animals.every(a => 
    a.purchasePrice && a.purchasePrice > 5000 && a.purchasePrice < 50000
  );
  if (!hasRealisticPrices) {
    recommendations.push('Ensure animal prices are realistic for South African market');
  }

  return {
    isValid: issues.length === 0,
    issues,
    recommendations,
    dataCount: {
      animals: data.animals.length,
      healthRecords: data.healthRecords.length,
      feedingRecords: data.feedingRecords.length,
      financialRecords: data.financialRecords.length
    }
  };
};

// Test BETA module access
export const testBetaModuleAccess = () => {
  const betaModules = [
    'animals',
    'health', 
    'feeding',
    'financial',
    'settings'
  ];

  const restrictedModules = [
    'breeding',
    'inventory',
    'commercial',
    'analytics',
    'compliance'
  ];

  return {
    allowedModules: betaModules,
    restrictedModules: restrictedModules,
    animalLimit: 50
  };
};

// Generate BETA test report
export const generateBetaTestReport = (data: BetaSampleData): string => {
  const validation = validateBetaData(data);
  const moduleAccess = testBetaModuleAccess();

  let report = '# BETA Data Validation Report\n\n';
  
  report += '## Data Summary\n';
  report += `- Animals: ${validation.dataCount.animals}\n`;
  report += `- Health Records: ${validation.dataCount.healthRecords}\n`;
  report += `- Feeding Records: ${validation.dataCount.feedingRecords}\n`;
  report += `- Financial Records: ${validation.dataCount.financialRecords}\n\n`;

  report += '## Validation Status\n';
  report += `Overall Status: ${validation.isValid ? '✅ PASSED' : '❌ FAILED'}\n\n`;

  if (validation.issues.length > 0) {
    report += '### Issues Found:\n';
    validation.issues.forEach(issue => {
      report += `- ❌ ${issue}\n`;
    });
    report += '\n';
  }

  if (validation.recommendations.length > 0) {
    report += '### Recommendations:\n';
    validation.recommendations.forEach(rec => {
      report += `- 💡 ${rec}\n`;
    });
    report += '\n';
  }

  report += '## Module Access\n';
  report += `### Allowed Modules (${moduleAccess.allowedModules.length}):\n`;
  moduleAccess.allowedModules.forEach(module => {
    report += `- ✅ ${module}\n`;
  });

  report += `\n### Restricted Modules (${moduleAccess.restrictedModules.length}):\n`;
  moduleAccess.restrictedModules.forEach(module => {
    report += `- ❌ ${module}\n`;
  });

  report += `\n### Limitations:\n`;
  report += `- Animal Limit: ${moduleAccess.animalLimit}\n`;
  report += `- Export Formats: Excel only\n`;
  report += `- Advanced Features: Disabled\n`;

  return report;
};
