/**
 * Global Background System for AgriIntel
 * Ensures consistent tinted backgrounds throughout the application
 * Removes all white backgrounds and applies modern blended design
 */

/* ===== GLOBAL BACKGROUND BASE ===== */

/* Root background - applies to entire application */
html, body {
  background: linear-gradient(135deg,
    rgba(21, 101, 192, 0.08) 0%,
    rgba(46, 125, 50, 0.08) 50%,
    rgba(245, 124, 0, 0.08) 100%
  ) !important;
  background-attachment: fixed !important;
  background-size: cover !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
  min-height: 100vh !important;
}

/* Application container background */
#root {
  background: transparent !important;
  min-height: 100vh !important;
}

/* ===== REMOVE WHITE BACKGROUNDS ===== */

/* Override all white backgrounds */
.MuiPaper-root,
.MuiCard-root,
.MuiDialog-paper,
.MuiPopover-paper,
.MuiMenu-paper,
.MuiDrawer-paper {
  background: var(--unified-bg-card) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Main content areas */
.main-content,
.dashboard-content,
.module-content,
.page-content {
  background: transparent !important;
}

/* Form backgrounds */
.MuiTextField-root .MuiOutlinedInput-root,
.MuiSelect-root,
.MuiFormControl-root {
  background: rgba(255, 255, 255, 0.8) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  backdrop-filter: blur(10px) !important;
}

/* ===== LANDING PAGE BACKGROUND ===== */

.agri-landing,
.landing-container,
.enhanced-landing-container {
  background: linear-gradient(135deg,
    rgba(46, 125, 50, 0.9) 0%,
    rgba(21, 101, 192, 0.8) 50%,
    rgba(245, 124, 0, 0.7) 100%
  ) !important;
  background-size: cover !important;
  background-position: center !important;
  background-attachment: fixed !important;
  background-repeat: no-repeat !important;
  min-height: 100vh !important;
}

/* ===== MAIN APP BACKGROUND ===== */

.dashboard-layout,
.unified-dashboard-layout,
.enhanced-dashboard-layout {
  background: linear-gradient(135deg,
    rgba(21, 101, 192, 0.08) 0%,
    rgba(46, 125, 50, 0.08) 50%,
    rgba(245, 124, 0, 0.08) 100%
  ) !important;
  background-size: cover !important;
  background-position: center !important;
  background-attachment: fixed !important;
  background-repeat: no-repeat !important;
  min-height: 100vh !important;
}

/* ===== LOGIN PAGE BACKGROUND ===== */

.login-container,
.split-screen-login,
.beta-v1-login-container,
.professional-v1-login-container {
  background: linear-gradient(135deg,
    rgba(245, 124, 0, 0.9) 0%,
    rgba(255, 183, 77, 0.8) 100%
  ) !important;
  background-size: cover !important;
  background-position: center !important;
  background-attachment: fixed !important;
  background-repeat: no-repeat !important;
  min-height: 100vh !important;
}

/* ===== NAVIGATION BACKGROUND ===== */

.agri-navigation,
.landing-navigation,
.nav-container {
  background: rgba(255, 255, 255, 0.1) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  backdrop-filter: blur(20px) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* ===== SIDEBAR BACKGROUND ===== */

.sidebar,
.modern-sidebar,
.MuiDrawer-paper {
  background: rgba(255, 255, 255, 0.15) !important;
  -webkit-backdrop-filter: blur(25px) !important;
  backdrop-filter: blur(25px) !important;
  border-right: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* ===== CARD BACKGROUNDS ===== */

.dashboard-card,
.module-card,
.stats-card,
.info-card {
  background: rgba(255, 255, 255, 0.85) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  backdrop-filter: blur(15px) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
}

/* ===== MODAL AND DIALOG BACKGROUNDS ===== */

.MuiDialog-paper,
.MuiModal-root .MuiPaper-root {
  background: rgba(255, 255, 255, 0.95) !important;
  -webkit-backdrop-filter: blur(30px) !important;
  backdrop-filter: blur(30px) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* ===== TABLE BACKGROUNDS ===== */

.MuiTableContainer-root,
.MuiTable-root {
  background: rgba(255, 255, 255, 0.8) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  backdrop-filter: blur(10px) !important;
}

.MuiTableHead-root {
  background: rgba(21, 101, 192, 0.1) !important;
}

.MuiTableRow-root:nth-of-type(even) {
  background: rgba(255, 255, 255, 0.5) !important;
}

/* ===== RESPONSIVE BACKGROUND ADJUSTMENTS ===== */

@media (max-width: 768px) {
  html, body {
    background-attachment: scroll !important;
  }
  
  .agri-landing,
  .dashboard-layout,
  .login-container {
    background-attachment: scroll !important;
  }
}

/* ===== ACCESSIBILITY OVERRIDES ===== */

@media (prefers-reduced-motion: reduce) {
  html, body,
  .agri-landing,
  .dashboard-layout,
  .login-container {
    background-attachment: scroll !important;
  }
}

/* ===== HIGH CONTRAST MODE ===== */

@media (prefers-contrast: high) {
  .MuiPaper-root,
  .MuiCard-root,
  .dashboard-card,
  .module-card {
    background: rgba(255, 255, 255, 0.98) !important;
    border: 2px solid #000000 !important;
  }
}

/* ===== PRINT STYLES ===== */

@media print {
  html, body,
  .agri-landing,
  .dashboard-layout,
  .login-container {
    background: white !important;
    background-image: none !important;
  }
  
  .MuiPaper-root,
  .MuiCard-root {
    background: white !important;
    -webkit-backdrop-filter: none !important;
    backdrop-filter: none !important;
  }
}
