/**
 * Add Animal Form Component
 * Example implementation using the Enhanced Form with validation
 */

import React, { useState } from 'react';
import { Dialog, DialogTitle, DialogContent, DialogActions, Button } from '@mui/material';
import EnhancedForm, { FormField } from '../forms/EnhancedForm';
import { ValidationRules, commonValidationRules } from '../../utils/formValidationUtils';
import { animalsAPI } from '../../services/api';
import { useSnackbar } from '../../hooks/useSnackbar';

interface AddAnimalFormProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const AddAnimalForm: React.FC<AddAnimalFormProps> = ({ open, onClose, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | undefined>(undefined);
  const { showSnackbar } = useSnackbar();

  // Define comprehensive form fields with lineage and lifecycle
  const formFields: FormField[] = [
    // Basic Information Section
    {
      name: 'tagNumber',
      label: 'Tag Number *',
      type: 'text',
      required: true,
      placeholder: 'e.g., A001, B-123',
      section: 'Basic Information'
    },
    {
      name: 'name',
      label: 'Animal Name',
      type: 'text',
      required: false,
      placeholder: 'Optional name for the animal',
      section: 'Basic Information'
    },
    {
      name: 'species',
      label: 'Species *',
      type: 'select',
      required: true,
      section: 'Basic Information',
      options: [
        { value: 'cattle', label: 'Cattle' },
        { value: 'sheep', label: 'Sheep' },
        { value: 'goat', label: 'Goat' },
        { value: 'pig', label: 'Pig' },
        { value: 'chicken', label: 'Chicken' },
        { value: 'other', label: 'Other' }
      ]
    },
    {
      name: 'breed',
      label: 'Breed Type *',
      type: 'select',
      required: true,
      section: 'Basic Information',
      options: [
        // Cattle breeds
        { value: 'holstein', label: 'Holstein' },
        { value: 'angus', label: 'Angus' },
        { value: 'brahman', label: 'Brahman' },
        { value: 'simmental', label: 'Simmental' },
        { value: 'charolais', label: 'Charolais' },
        { value: 'hereford', label: 'Hereford' },
        { value: 'limousin', label: 'Limousin' },
        { value: 'bonsmara', label: 'Bonsmara' },
        { value: 'afrikaner', label: 'Afrikaner' },
        { value: 'nguni', label: 'Nguni' },
        // Sheep breeds
        { value: 'merino', label: 'Merino' },
        { value: 'dorper', label: 'Dorper' },
        { value: 'blackhead_persian', label: 'Blackhead Persian' },
        { value: 'damara', label: 'Damara' },
        // Goat breeds
        { value: 'boer', label: 'Boer' },
        { value: 'angora', label: 'Angora' },
        { value: 'saanen', label: 'Saanen' },
        // Other
        { value: 'crossbreed', label: 'Crossbreed' },
        { value: 'other', label: 'Other' }
      ]
    },
    {
      name: 'gender',
      label: 'Gender *',
      type: 'select',
      required: true,
      section: 'Basic Information',
      options: [
        { value: 'male', label: 'Male' },
        { value: 'female', label: 'Female' },
        { value: 'unknown', label: 'Unknown' }
      ]
    },
    {
      name: 'birthDate',
      label: 'Birth Date',
      type: 'date',
      required: false,
      section: 'Basic Information',
      helpText: 'Used for automatic lifecycle stage calculation'
    },
    {
      name: 'age',
      label: 'Age (months)',
      type: 'number',
      required: false,
      section: 'Basic Information',
      placeholder: 'Age in months if birth date unknown',
      helpText: 'Alternative to birth date for lifecycle calculation'
    },
    {
      name: 'acquisitionDate',
      label: 'Acquisition Date *',
      type: 'date',
      required: true,
      section: 'Basic Information'
    },
    {
      name: 'weight',
      label: 'Weight (kg)',
      type: 'number',
      required: false,
      placeholder: 'Current weight in kilograms',
      section: 'Physical Characteristics'
    },
    {
      name: 'location',
      label: 'Location/Pen',
      type: 'text',
      required: false,
      placeholder: 'e.g., Pen A, Pasture 1, Barn 2',
      section: 'Location & Identification'
    },
    {
      name: 'rfidTag',
      label: 'RFID Tag',
      type: 'text',
      required: false,
      placeholder: 'RFID tag number if available',
      section: 'Location & Identification'
    },

    // Maternal Lineage Section
    {
      name: 'damTagNumber',
      label: 'Mother Tag Number',
      type: 'text',
      required: false,
      placeholder: 'Tag number of the mother',
      section: 'Maternal Lineage'
    },
    {
      name: 'damName',
      label: 'Mother Name',
      type: 'text',
      required: false,
      placeholder: 'Name of the mother',
      section: 'Maternal Lineage'
    },
    {
      name: 'damBreed',
      label: 'Mother Breed',
      type: 'text',
      required: false,
      placeholder: 'Breed of the mother',
      section: 'Maternal Lineage'
    },
    {
      name: 'maternalGrandmotherTag',
      label: 'Maternal Grandmother Tag',
      type: 'text',
      required: false,
      placeholder: 'Tag number of maternal grandmother',
      section: 'Maternal Lineage'
    },

    // Paternal Lineage Section
    {
      name: 'sireTagNumber',
      label: 'Father Tag Number',
      type: 'text',
      required: false,
      placeholder: 'Tag number of the father',
      section: 'Paternal Lineage'
    },
    {
      name: 'sireName',
      label: 'Father Name',
      type: 'text',
      required: false,
      placeholder: 'Name of the father',
      section: 'Paternal Lineage'
    },
    {
      name: 'sireBreed',
      label: 'Father Breed',
      type: 'text',
      required: false,
      placeholder: 'Breed of the father',
      section: 'Paternal Lineage'
    },
    {
      name: 'paternalGrandfatherTag',
      label: 'Paternal Grandfather Tag',
      type: 'text',
      required: false,
      placeholder: 'Tag number of paternal grandfather',
      section: 'Paternal Lineage'
    },

    // Health Status Section
    {
      name: 'healthStatus',
      label: 'Health Status',
      type: 'select',
      required: false,
      section: 'Health Information',
      options: [
        { value: 'healthy', label: 'Healthy' },
        { value: 'sick', label: 'Sick' },
        { value: 'injured', label: 'Injured' },
        { value: 'recovering', label: 'Recovering' },
        { value: 'quarantined', label: 'Quarantined' },
        { value: 'unknown', label: 'Unknown' }
      ]
    },
    {
      name: 'purchasePrice',
      label: 'Purchase Price (ZAR)',
      type: 'number',
      required: false,
      placeholder: 'Purchase price in South African Rand',
      section: 'Financial Information'
    },
    {
      name: 'notes',
      label: 'Additional Notes',
      type: 'textarea',
      required: false,
      placeholder: 'Any additional information about the animal',
      section: 'Additional Information',
      rows: 3
    }
  ];

  // Define validation rules
  const validationRules: ValidationRules = {
    tagNumber: {
      required: true,
      pattern: /^[A-Z0-9-]+$/,
      custom: (value: string) => {
        if (value && !/^[A-Z0-9-]+$/.test(value)) {
          return 'Tag number can only contain uppercase letters, numbers, and hyphens';
        }
        if (value && value.length < 2) {
          return 'Tag number must be at least 2 characters long';
        }
        return null;
      }
    },
    name: {
      ...commonValidationRules.name,
      required: false
    },
    species: {
      required: true
    },
    gender: {
      required: true
    },
    acquisitionDate: {
      required: true,
      custom: (value: any) => {
        if (value && isNaN(Date.parse(value))) {
          return 'Please enter a valid date';
        }
        if (value && new Date(value) > new Date()) {
          return 'Acquisition date cannot be in the future';
        }
        return null;
      }
    },
    birthDate: {
      custom: (value: any) => {
        if (value && isNaN(Date.parse(value))) {
          return 'Please enter a valid date';
        }
        if (value && new Date(value) > new Date()) {
          return 'Birth date cannot be in the future';
        }
        return null;
      }
    },
    weight: {
      custom: (value: any) => {
        if (value) {
          const num = parseFloat(value);
          if (isNaN(num)) {
            return 'Weight must be a valid number';
          }
          if (num <= 0) {
            return 'Weight must be greater than 0';
          }
          if (num > 2000) {
            return 'Weight seems unrealistic (max 2000kg)';
          }
        }
        return null;
      }
    },
    rfidTag: {
      pattern: /^[A-Z0-9]+$/,
      custom: (value: string) => {
        if (value && !/^[A-Z0-9]+$/.test(value)) {
          return 'RFID tag can only contain uppercase letters and numbers';
        }
        return null;
      }
    }
  };

  // Initial form values with comprehensive fields
  const initialValues = {
    // Basic Information
    tagNumber: '',
    name: '',
    species: '',
    breed: '',
    gender: '',
    birthDate: '',
    age: '',
    acquisitionDate: new Date().toISOString().split('T')[0], // Today's date
    weight: '',
    location: '',
    rfidTag: '',

    // Maternal Lineage
    damTagNumber: '',
    damName: '',
    damBreed: '',
    maternalGrandmotherTag: '',

    // Paternal Lineage
    sireTagNumber: '',
    sireName: '',
    sireBreed: '',
    paternalGrandfatherTag: '',

    // Health and Financial
    healthStatus: 'unknown',
    purchasePrice: '',
    notes: ''
  };

  // Calculate lifecycle stage based on age
  const calculateLifecycleStage = (birthDate: string | null, ageInMonths: number | null, species: string): string => {
    let ageMonths = ageInMonths;

    if (birthDate && !ageMonths) {
      const birth = new Date(birthDate);
      const now = new Date();
      ageMonths = Math.floor((now.getTime() - birth.getTime()) / (1000 * 60 * 60 * 24 * 30.44));
    }

    if (!ageMonths || ageMonths < 0) return 'calf';

    // Species-specific lifecycle stages
    const lifecycleStages = {
      cattle: {
        calf: { min: 0, max: 6 },
        juvenile: { min: 6, max: 24 },
        adult: { min: 24, max: 120 },
        retirement: { min: 120, max: null }
      },
      sheep: {
        calf: { min: 0, max: 4 },
        juvenile: { min: 4, max: 12 },
        adult: { min: 12, max: 96 },
        retirement: { min: 96, max: null }
      },
      goat: {
        calf: { min: 0, max: 4 },
        juvenile: { min: 4, max: 12 },
        adult: { min: 12, max: 96 },
        retirement: { min: 96, max: null }
      },
      pig: {
        calf: { min: 0, max: 2 },
        juvenile: { min: 2, max: 6 },
        adult: { min: 6, max: 48 },
        retirement: { min: 48, max: null }
      },
      chicken: {
        calf: { min: 0, max: 1 },
        juvenile: { min: 1, max: 6 },
        adult: { min: 6, max: 24 },
        retirement: { min: 24, max: null }
      }
    };

    const stages = lifecycleStages[species as keyof typeof lifecycleStages] || lifecycleStages.cattle;

    for (const [stage, range] of Object.entries(stages)) {
      if (ageMonths >= range.min && (range.max === null || ageMonths < range.max)) {
        return stage;
      }
    }

    return 'retirement';
  };

  // Handle form submission with enhanced data processing
  const handleSubmit = async (values: Record<string, any>) => {
    setLoading(true);
    setError(undefined);

    try {
      // Calculate lifecycle stage
      const lifecycleStage = calculateLifecycleStage(
        values.birthDate,
        values.age ? parseInt(values.age) : null,
        values.species
      );

      // Prepare comprehensive animal data
      const animalData = {
        // Basic Information
        tagNumber: values.tagNumber,
        name: values.name || '',
        species: values.species,
        breed: values.breed,
        gender: values.gender,
        birthDate: values.birthDate ? new Date(values.birthDate) : null,
        acquisitionDate: new Date(values.acquisitionDate),
        weight: values.weight ? parseFloat(values.weight) : null,
        location: values.location || '',
        rfidTag: values.rfidTag || '',

        // Status Information
        status: 'active',
        healthStatus: values.healthStatus || 'unknown',

        // Lifecycle Management
        lifecycle: {
          currentStage: lifecycleStage,
          stageHistory: [{
            stage: lifecycleStage,
            startDate: new Date(),
            endDate: null,
            autoTransitioned: true,
            notes: `Initial stage set based on age calculation`
          }]
        },

        // Genealogy Information
        genealogy: {
          sire: {
            tagNumber: values.sireTagNumber || '',
            name: values.sireName || '',
            breed: values.sireBreed || ''
          },
          dam: {
            tagNumber: values.damTagNumber || '',
            name: values.damName || '',
            breed: values.damBreed || ''
          },
          maternalGrandmother: {
            tagNumber: values.maternalGrandmotherTag || ''
          },
          paternalGrandfather: {
            tagNumber: values.paternalGrandfatherTag || ''
          }
        },

        // Financial Information
        financial: {
          purchasePrice: values.purchasePrice ? parseFloat(values.purchasePrice) : null,
          estimatedValue: values.purchasePrice ? parseFloat(values.purchasePrice) : null
        },

        // Additional Information
        notes: values.notes || '',

        // Metadata
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Submit to API
      await animalsAPI.createAnimal(animalData);

      showSnackbar(`Animal added successfully! Lifecycle stage: ${lifecycleStage}`, 'success');
      onSuccess();
      onClose();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add animal';
      setError(errorMessage);
      showSnackbar(errorMessage, 'error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(20px)',
          border: '1px solid rgba(255, 255, 255, 0.2)'
        }
      }}
    >
      <DialogTitle sx={{
        color: '#ffffff',
        fontSize: '1.5rem',
        fontWeight: 700,
        textAlign: 'center',
        background: 'linear-gradient(135deg, #2E7D32, #4CAF50)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent'
      }}>
        🐄 Add New Animal - Comprehensive Registration
      </DialogTitle>
      <DialogContent sx={{ color: '#ffffff', padding: '24px' }}>
        <div style={{
          marginBottom: '20px',
          padding: '16px',
          background: 'rgba(76, 175, 80, 0.1)',
          borderRadius: '12px',
          border: '1px solid rgba(76, 175, 80, 0.3)'
        }}>
          <p style={{ margin: 0, fontSize: '0.9rem', color: 'rgba(255, 255, 255, 0.9)' }}>
            ℹ️ <strong>Smart Features:</strong> Automatic lifecycle stage detection (calf → juvenile → adult → retirement)
            based on age. Complete lineage tracking for breeding management. All fields marked with * are required.
          </p>
        </div>

        <EnhancedForm
          fields={formFields}
          initialValues={initialValues}
          validationRules={validationRules}
          onSubmit={handleSubmit}
          onCancel={onClose}
          submitButtonText="🚀 Add Animal with Smart Lifecycle"
          loading={loading}
          error={error}
        />
      </DialogContent>
    </Dialog>
  );
};

export default AddAnimalForm;
