import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Avatar,
  Chip,
  Rating,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  useTheme,
  alpha,
  Divider,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  IconButton,
  Badge,
  Container,
  Paper,
  Tab,
  Tabs,
  Alert,
  Fab,
  ListItemSecondaryAction
} from '@mui/material';
import {
  LocalHospital,
  Store,
  Gavel,
  Security,
  Star,
  Phone,
  Message,
  LocationOn,
  Schedule,
  MonetizationOn,
  Emergency,
  CheckCircle,
  Cancel,
  Send,
  Notifications,
  WhatsApp,
  Email,
  Add,
  FilterList
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useLanguage } from '../../contexts/LanguageContext';
import { useAuth } from '../../contexts/AuthContext';
import AgriIntelBrand from '../branding/AgriIntelBrand';

interface ServiceProvider {
  id: string;
  name: string;
  type: 'veterinarian' | 'supplier' | 'auctioneer' | 'security';
  rating: number;
  reviewCount: number;
  location: string;
  distance: number;
  available: boolean;
  responseTime: string;
  priceRange: string;
  specialties: string[];
  avatar: string;
  verified: boolean;
  emergencyService: boolean;
}

interface ServiceRequest {
  id: string;
  type: 'veterinarian' | 'supplier' | 'auctioneer' | 'security';
  title: string;
  description: string;
  urgency: 'low' | 'medium' | 'high' | 'emergency';
  location: string;
  budget: number;
  status: 'pending' | 'accepted' | 'in_progress' | 'completed' | 'cancelled';
  createdAt: string;
  providerId?: string;
  providerName?: string;
}

const ServiceMarketplace: React.FC = () => {
  const theme = useTheme();
  const { translate } = useLanguage();
  const { user, canAccessModule } = useAuth();

  const [currentTab, setCurrentTab] = useState(0);
  const [selectedService, setSelectedService] = useState<string>('veterinarian');
  const [showRequestDialog, setShowRequestDialog] = useState(false);
  const [showProviderDialog, setShowProviderDialog] = useState(false);
  const [emergencyDialogOpen, setEmergencyDialogOpen] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<ServiceProvider | null>(null);
  const [activeRequests, setActiveRequests] = useState<ServiceRequest[]>([]);
  const [messages, setMessages] = useState<any[]>([]);
  const [newRequest, setNewRequest] = useState({
    type: 'veterinarian' as const,
    title: '',
    description: '',
    urgency: 'medium' as const,
    location: '',
    budget: 0
  });

  // Check access control
  if (!canAccessModule('marketplace')) {
    return (
      <Container maxWidth="lg" sx={{ py: 8, textAlign: 'center' }}>
        <Alert severity="warning" sx={{ mb: 4 }}>
          <Typography variant="h5" sx={{ mb: 2 }}>
            Marketplace Access Required
          </Typography>
          <Typography variant="body1" sx={{ mb: 3 }}>
            The Service Marketplace is available for Professional and Enterprise subscribers.
            Upgrade your plan to connect with veterinarians, suppliers, auctioneers, and security services.
          </Typography>
          <Button variant="contained" color="primary" size="large">
            Upgrade to Professional - R699/month
          </Button>
        </Alert>
      </Container>
    );
  }

  // Sample service providers
  const serviceProviders: ServiceProvider[] = [
    {
      id: 'vet1',
      name: 'Dr. Sarah Johnson',
      type: 'veterinarian',
      rating: 4.8,
      reviewCount: 127,
      location: 'Pretoria East',
      distance: 12.5,
      available: true,
      responseTime: '< 30 min',
      priceRange: 'R800 - R1500',
      specialties: ['Cattle', 'Emergency Care', 'Surgery'],
      avatar: '/images/providers/vet1.jpg',
      verified: true,
      emergencyService: true
    },
    {
      id: 'supplier1',
      name: 'AgriSupply Pro',
      type: 'supplier',
      rating: 4.6,
      reviewCount: 89,
      location: 'Centurion',
      distance: 8.2,
      available: true,
      responseTime: '< 2 hours',
      priceRange: 'Competitive',
      specialties: ['Feed', 'Equipment', 'Medication'],
      avatar: '/images/providers/supplier1.jpg',
      verified: true,
      emergencyService: false
    },
    {
      id: 'auctioneer1',
      name: 'Livestock Auctions SA',
      type: 'auctioneer',
      rating: 4.9,
      reviewCount: 156,
      location: 'Johannesburg',
      distance: 45.0,
      available: true,
      responseTime: '< 24 hours',
      priceRange: '5% commission',
      specialties: ['Cattle Auctions', 'Online Sales', 'Valuation'],
      avatar: '/images/providers/auctioneer1.jpg',
      verified: true,
      emergencyService: false
    },
    {
      id: 'security1',
      name: 'Farm Security Solutions',
      type: 'security',
      rating: 4.7,
      reviewCount: 73,
      location: 'Gauteng Region',
      distance: 15.0,
      available: true,
      responseTime: '< 15 min',
      priceRange: 'R500 - R2000',
      specialties: ['24/7 Monitoring', 'Emergency Response', 'Theft Prevention'],
      avatar: '/images/providers/security1.jpg',
      verified: true,
      emergencyService: true
    }
  ];

  const serviceTypes = [
    { id: 'veterinarian', label: 'Veterinary Services', icon: <LocalHospital />, color: '#2E7D32' },
    { id: 'supplier', label: 'Suppliers', icon: <Store />, color: '#1976D2' },
    { id: 'auctioneer', label: 'Auctioneers', icon: <Gavel />, color: '#F57C00' },
    { id: 'security', label: 'Security Services', icon: <Security />, color: '#D32F2F' }
  ];

  const filteredProviders = serviceProviders.filter(provider => provider.type === selectedService);

  const handleRequestService = () => {
    const request: ServiceRequest = {
      id: Date.now().toString(),
      type: newRequest.type,
      title: newRequest.title,
      description: newRequest.description,
      urgency: newRequest.urgency,
      location: newRequest.location,
      budget: newRequest.budget,
      status: 'pending',
      createdAt: new Date().toISOString()
    };
    
    setActiveRequests([...activeRequests, request]);
    setShowRequestDialog(false);
    setNewRequest({
      type: 'veterinarian',
      title: '',
      description: '',
      urgency: 'medium',
      location: '',
      budget: 0
    });
  };

  const handleContactProvider = (provider: ServiceProvider) => {
    setSelectedProvider(provider);
    setShowProviderDialog(true);
  };

  const handleWhatsAppContact = (provider: ServiceProvider, message?: string) => {
    const defaultMessage = `Hi ${provider.name}, I'm a farmer using AgriIntel and need ${provider.type} services. Can you help me?`;
    const whatsappMessage = message || defaultMessage;
    const phoneNumber = '27794484159'; // AgriIntel contact number for demo
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(whatsappMessage)}`;
    window.open(whatsappUrl, '_blank');
  };

  const handleEmergencyRequest = () => {
    setEmergencyDialogOpen(true);
  };

  const handleEmergencySubmit = (urgencyType: string) => {
    const emergencyMessage = `🚨 EMERGENCY REQUEST via AgriIntel 🚨
Farm Emergency: ${urgencyType}
Farmer: ${user?.firstName} ${user?.lastName}
Contact: **********
Location: [Farm Location]
Time: ${new Date().toLocaleString()}

Please respond immediately!`;

    handleWhatsAppContact({ name: 'Emergency Services', type: 'veterinarian' } as ServiceProvider, emergencyMessage);
    setEmergencyDialogOpen(false);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'emergency': return theme.palette.error.main;
      case 'high': return theme.palette.warning.main;
      case 'medium': return theme.palette.info.main;
      case 'low': return theme.palette.success.main;
      default: return theme.palette.grey[500];
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4, textAlign: 'center' }}>
        <AgriIntelBrand variant="compact" size="large" color="primary" />
        <Typography variant="h3" sx={{ mt: 2, mb: 1, fontWeight: 700 }}>
          Service Marketplace
        </Typography>
        <Typography variant="h6" color="text.secondary">
          Connect with trusted agricultural service providers across South Africa
        </Typography>
      </Box>

      {/* Emergency Button */}
      <Fab
        color="error"
        sx={{
          position: 'fixed',
          bottom: 20,
          right: 20,
          zIndex: 1000
        }}
        onClick={handleEmergencyRequest}
      >
        <Emergency />
      </Fab>

      {/* Navigation Tabs */}
      <Paper sx={{ mb: 4 }}>
        <Tabs value={currentTab} onChange={handleTabChange} centered>
          <Tab label="Find Services" />
          <Tab label="My Requests" />
          <Tab label="Messages" />
        </Tabs>
      </Paper>

      {/* Tab Content */}
      {currentTab === 0 && (
        <Box>
          {/* Service Type Selector */}
          <Grid container spacing={2} sx={{ mb: 4 }}>
            {serviceTypes.map((service) => (
              <Grid item xs={6} md={3} key={service.id}>
                <Card
                  sx={{
                    cursor: 'pointer',
                    border: selectedService === service.id ? `2px solid ${service.color}` : '1px solid rgba(255,255,255,0.1)',
                    backgroundColor: selectedService === service.id ? alpha(service.color, 0.1) : 'transparent',
                    transition: 'all 0.3s ease',
                    '&:hover': { transform: 'translateY(-2px)' }
                  }}
                  onClick={() => setSelectedService(service.id)}
                >
                  <CardContent sx={{ textAlign: 'center', py: 2 }}>
                    <Box sx={{ color: service.color, mb: 1 }}>
                      {service.icon}
                    </Box>
                    <Typography variant="subtitle2" fontWeight="bold">
                      {service.label}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {filteredProviders.length} available
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>

          {/* Request Service Button */}
          <Box sx={{ mb: 4, textAlign: 'center' }}>
            <Button
              variant="contained"
              size="large"
              startIcon={<Add />}
              onClick={() => setShowRequestDialog(true)}
              sx={{
                background: 'linear-gradient(45deg, #22C55E, #16A34A)',
                px: 4,
                py: 1.5
              }}
            >
              Request Service
            </Button>
          </Box>

      {/* Active Requests */}
      {activeRequests.length > 0 && (
        <Card sx={{ mb: 4, backgroundColor: alpha(theme.palette.info.main, 0.05) }}>
          <CardContent>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              Active Service Requests
            </Typography>
            <List>
              {activeRequests.slice(-3).map((request) => (
                <ListItem key={request.id} sx={{ px: 0 }}>
                  <ListItemAvatar>
                    <Avatar sx={{ backgroundColor: getUrgencyColor(request.urgency) }}>
                      <Notifications />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={request.title}
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          {request.description}
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                          <Chip label={request.urgency} size="small" sx={{ backgroundColor: getUrgencyColor(request.urgency), color: 'white' }} />
                          <Chip label={request.status} size="small" variant="outlined" />
                        </Box>
                      </Box>
                    }
                  />
                  <Typography variant="body2" color="text.secondary">
                    R{request.budget}
                  </Typography>
                </ListItem>
              ))}
            </List>
          </CardContent>
        </Card>
      )}

      {/* Service Providers */}
      <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
        Available {serviceTypes.find(s => s.id === selectedService)?.label}
      </Typography>

      <Grid container spacing={3}>
        {filteredProviders.map((provider, index) => (
          <Grid item xs={12} md={6} lg={4} key={provider.id}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ y: -4 }}
            >
              <Card sx={{ 
                height: '100%',
                borderRadius: 3,
                background: 'rgba(255,255,255,0.05)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255,255,255,0.1)'
              }}>
                <CardContent sx={{ p: 3 }}>
                  {/* Provider Header */}
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar
                      src={provider.avatar}
                      sx={{ width: 56, height: 56, mr: 2 }}
                    >
                      {provider.name.charAt(0)}
                    </Avatar>
                    <Box sx={{ flexGrow: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="h6" fontWeight="bold">
                          {provider.name}
                        </Typography>
                        {provider.verified && (
                          <CheckCircle sx={{ color: theme.palette.success.main, fontSize: 20 }} />
                        )}
                        {provider.emergencyService && (
                          <Chip label="24/7" size="small" color="error" />
                        )}
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Rating value={provider.rating} precision={0.1} size="small" readOnly />
                        <Typography variant="body2" color="text.secondary">
                          ({provider.reviewCount})
                        </Typography>
                      </Box>
                    </Box>
                    {provider.available && (
                      <Chip 
                        label="Available" 
                        size="small" 
                        color="success" 
                        variant="outlined"
                      />
                    )}
                  </Box>

                  {/* Provider Details */}
                  <Box sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <LocationOn sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
                      <Typography variant="body2" color="text.secondary">
                        {provider.location} • {provider.distance}km away
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Schedule sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
                      <Typography variant="body2" color="text.secondary">
                        Response time: {provider.responseTime}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <MonetizationOn sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
                      <Typography variant="body2" color="text.secondary">
                        {provider.priceRange}
                      </Typography>
                    </Box>
                  </Box>

                  {/* Specialties */}
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Specialties:
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {provider.specialties.map((specialty, idx) => (
                        <Chip 
                          key={idx}
                          label={specialty} 
                          size="small" 
                          variant="outlined"
                          sx={{ fontSize: '0.75rem' }}
                        />
                      ))}
                    </Box>
                  </Box>

                  {/* Action Buttons */}
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      variant="contained"
                      fullWidth
                      startIcon={<WhatsApp />}
                      onClick={() => handleWhatsAppContact(provider)}
                      disabled={!provider.available}
                      sx={{
                        background: '#25D366',
                        '&:hover': { background: '#20BA5A' }
                      }}
                    >
                      WhatsApp
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<Phone />}
                      onClick={() => handleContactProvider(provider)}
                      disabled={!provider.available}
                      sx={{ minWidth: 'auto' }}
                    >
                      Call
                    </Button>
                    {provider.emergencyService && (
                      <Button
                        variant="outlined"
                        color="error"
                        startIcon={<Emergency />}
                        onClick={() => handleEmergencyRequest()}
                        sx={{ minWidth: 'auto' }}
                      >
                        SOS
                      </Button>
                    )}
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
          </Grid>
        </Box>
      )}

      {/* My Requests Tab */}
      {currentTab === 1 && (
        <Box>
          <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
            My Service Requests
          </Typography>
          {activeRequests.length === 0 ? (
            <Paper sx={{ p: 4, textAlign: 'center' }}>
              <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
                No active requests
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Start by requesting a service from our trusted providers
              </Typography>
              <Button
                variant="contained"
                onClick={() => setCurrentTab(0)}
                sx={{ background: 'linear-gradient(45deg, #22C55E, #16A34A)' }}
              >
                Find Services
              </Button>
            </Paper>
          ) : (
            <Grid container spacing={3}>
              {activeRequests.map((request) => (
                <Grid item xs={12} md={6} key={request.id}>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start', mb: 2 }}>
                        <Typography variant="h6" fontWeight="bold">
                          {request.title}
                        </Typography>
                        <Chip
                          label={request.status}
                          size="small"
                          color={request.status === 'completed' ? 'success' : 'primary'}
                        />
                      </Box>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        {request.description}
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                        <Chip
                          label={request.urgency}
                          size="small"
                          sx={{ backgroundColor: getUrgencyColor(request.urgency), color: 'white' }}
                        />
                        <Chip label={`R${request.budget}`} size="small" variant="outlined" />
                      </Box>
                      <Typography variant="caption" color="text.secondary">
                        Created: {new Date(request.createdAt).toLocaleDateString()}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}
        </Box>
      )}

      {/* Messages Tab */}
      {currentTab === 2 && (
        <Box>
          <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
            Messages
          </Typography>
          <Paper sx={{ p: 4, textAlign: 'center' }}>
            <Message sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
              No messages yet
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              All communication with service providers happens through WhatsApp for security and convenience
            </Typography>
            <Button
              variant="contained"
              startIcon={<WhatsApp />}
              onClick={() => window.open('https://wa.me/27794484159', '_blank')}
              sx={{
                background: '#25D366',
                '&:hover': { background: '#20BA5A' }
              }}
            >
              Open WhatsApp
            </Button>
          </Paper>
        </Box>
      )}

      {/* Emergency Dialog */}
      <Dialog open={emergencyDialogOpen} onClose={() => setEmergencyDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ color: 'error.main', display: 'flex', alignItems: 'center', gap: 1 }}>
          <Emergency />
          Emergency Request
        </DialogTitle>
        <DialogContent>
          <Alert severity="error" sx={{ mb: 3 }}>
            This will send an immediate emergency request to all available service providers in your area.
          </Alert>
          <Typography variant="body1" sx={{ mb: 3 }}>
            Select the type of emergency:
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <Button
                fullWidth
                variant="outlined"
                color="error"
                onClick={() => handleEmergencySubmit('Veterinary Emergency')}
                sx={{ py: 2 }}
              >
                <Box sx={{ textAlign: 'center' }}>
                  <LocalHospital sx={{ fontSize: 32, mb: 1 }} />
                  <Typography variant="body2">Veterinary Emergency</Typography>
                </Box>
              </Button>
            </Grid>
            <Grid item xs={6}>
              <Button
                fullWidth
                variant="outlined"
                color="error"
                onClick={() => handleEmergencySubmit('Security Emergency')}
                sx={{ py: 2 }}
              >
                <Box sx={{ textAlign: 'center' }}>
                  <Security sx={{ fontSize: 32, mb: 1 }} />
                  <Typography variant="body2">Security Emergency</Typography>
                </Box>
              </Button>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEmergencyDialogOpen(false)}>Cancel</Button>
        </DialogActions>
      </Dialog>

      {/* Request Service Dialog */}
      <Dialog open={showRequestDialog} onClose={() => setShowRequestDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Request Service</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Service Type</InputLabel>
                <Select
                  value={newRequest.type}
                  onChange={(e) => setNewRequest({ ...newRequest, type: e.target.value as any })}
                  label="Service Type"
                >
                  {serviceTypes.map((service) => (
                    <MenuItem key={service.id} value={service.id}>
                      {service.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Urgency</InputLabel>
                <Select
                  value={newRequest.urgency}
                  onChange={(e) => setNewRequest({ ...newRequest, urgency: e.target.value as any })}
                  label="Urgency"
                >
                  <MenuItem value="low">Low</MenuItem>
                  <MenuItem value="medium">Medium</MenuItem>
                  <MenuItem value="high">High</MenuItem>
                  <MenuItem value="emergency">Emergency</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Service Title"
                value={newRequest.title}
                onChange={(e) => setNewRequest({ ...newRequest, title: e.target.value })}
                placeholder="e.g., Cattle health check, Feed delivery"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                value={newRequest.description}
                onChange={(e) => setNewRequest({ ...newRequest, description: e.target.value })}
                multiline
                rows={3}
                placeholder="Describe what you need..."
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Location"
                value={newRequest.location}
                onChange={(e) => setNewRequest({ ...newRequest, location: e.target.value })}
                placeholder="Farm location or address"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Budget (R)"
                type="number"
                value={newRequest.budget}
                onChange={(e) => setNewRequest({ ...newRequest, budget: Number(e.target.value) })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowRequestDialog(false)}>Cancel</Button>
          <Button variant="contained" onClick={handleRequestService}>
            Send Request
          </Button>
        </DialogActions>
      </Dialog>

      {/* Provider Contact Dialog */}
      <Dialog open={showProviderDialog} onClose={() => setShowProviderDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Contact {selectedProvider?.name}</DialogTitle>
        <DialogContent>
          {selectedProvider && (
            <Box>
              <Typography variant="body1" gutterBottom>
                You're about to contact {selectedProvider.name} for {selectedProvider.type} services.
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Response time: {selectedProvider.responseTime}
              </Typography>
              <TextField
                fullWidth
                label="Message"
                multiline
                rows={4}
                placeholder="Describe your needs..."
                sx={{ mb: 2 }}
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowProviderDialog(false)}>Cancel</Button>
          <Button variant="contained" startIcon={<Send />}>
            Send Message
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default ServiceMarketplace;
