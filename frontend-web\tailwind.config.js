/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./src/**/*.{js,jsx,ts,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#e6f7f6',
          100: '#c2ebe8',
          200: '#9cdedb',
          300: '#75d0cd',
          400: '#4ec3bf',
          500: '#27b5b0',
          600: '#1f958f',
          700: '#17756f',
          800: '#0F766E', // Primary teal color from README
          900: '#085752',
        },
        secondary: {
          50: '#f8f9fa',
          100: '#f1f3f5',
          200: '#e9ecef',
          300: '#dee2e6',
          400: '#ced4da',
          500: '#adb5bd',
          600: '#868e96',
          700: '#495057',
          800: '#374151', // Secondary gray color from README
          900: '#212529',
        },
        accent: {
          50: '#fff3e0',
          100: '#ffe0b2',
          200: '#ffcc80',
          300: '#ffb74d',
          400: '#ffa726',
          500: '#ff9800',
          600: '#f57c00',
          700: '#ef6c00',
          800: '#e65100',
          900: '#F57C00', // Accent gold color for AgriIntel
        },
        success: '#059669', // Success green from README
        warning: '#D97706', // Warning amber from README
        danger: '#DC2626', // Danger red from README
      },
      fontFamily: {
        sans: ['Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
        heading: ['Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
        body: ['Roboto', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Helvetica Neue', 'Arial', 'sans-serif'],
        mono: ['JetBrains Mono', 'ui-monospace', 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace'],
      },
      backdropBlur: {
        xs: '2px',
      },
      boxShadow: {
        card: '0 2px 4px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1)',
        'card-hover': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      },
      borderRadius: {
        'card': '0.5rem',
      },
      backgroundImage: {
        'gradient-primary': 'linear-gradient(135deg, var(--color-primary-800), var(--color-primary-600))',
        'gradient-secondary': 'linear-gradient(135deg, var(--color-secondary-800), var(--color-secondary-600))',
        'gradient-accent': 'linear-gradient(135deg, var(--color-accent-900), var(--color-accent-700))',
      },
    },
  },
  plugins: []
}
