/*
 * AGRIINTEL SPLIT-SCREEN LOGIN LAYOUT
 * Professional two-panel design with image and form
 */

/* Split Screen Container */
.split-screen-login {
  display: flex;
  min-height: 100vh;
  width: 100%;
  overflow: hidden;
}

/* Left Panel - Image Section */
.login-image-panel {
  flex: 1;
  position: relative;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-image-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1;
}

.login-image-overlay {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  padding: 2rem;
}

.login-image-title {
  font-size: 3rem !important;
  font-weight: 800 !important;
  margin-bottom: 1rem !important;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.login-image-subtitle {
  font-size: 1.5rem !important;
  font-weight: 300 !important;
  opacity: 0.9;
  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.5);
}

/* Right Panel - Form Section */
.login-form-panel {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--unified-bg-overlay);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  padding: 2rem;
  position: relative;
  border-left: 1px solid rgba(255, 255, 255, 0.2);
}

.login-form-container {
  width: 100%;
  max-width: 400px;
  padding: 2rem;
}

/* BETA Styling */
.split-screen-login.beta .login-image-panel {
  /* background-image: url('/images/animals/cattle-1.jpeg'); */
}

.split-screen-login.beta .login-form-panel {
  background: linear-gradient(135deg,
    rgba(255, 152, 0, 0.05) 0%,
    rgba(255, 183, 77, 0.08) 50%,
    rgba(255, 255, 255, 0.95) 100%
  );
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border-left: 1px solid rgba(255, 152, 0, 0.2);
}

.split-screen-login.beta .login-brand-badge {
  background: linear-gradient(135deg, #FF9800, #FFA726) !important;
  color: white !important;
  font-weight: 700 !important;
}

.split-screen-login.beta .login-primary-button {
  background: linear-gradient(135deg, #FF9800, #FFA726) !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3) !important;
}

.split-screen-login.beta .login-primary-button:hover {
  background: linear-gradient(135deg, #F57C00, #FF9800) !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 152, 0, 0.4) !important;
}

/* Professional Styling */
.split-screen-login.professional .login-image-panel {
  /* background-image: url('/images/animals/cattle-3.jpeg'); */
}

.split-screen-login.professional .login-form-panel {
  background: linear-gradient(135deg,
    rgba(46, 125, 50, 0.05) 0%,
    rgba(255, 255, 255, 0.95) 100%);
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  border-left: 1px solid rgba(46, 125, 50, 0.2);
}

.split-screen-login.professional .login-brand-badge {
  background: linear-gradient(135deg, #4CAF50, #66BB6A) !important;
  color: white !important;
  font-weight: 700 !important;
}

.split-screen-login.professional .login-primary-button {
  background: linear-gradient(135deg, #4CAF50, #66BB6A) !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3) !important;
}

.split-screen-login.professional .login-primary-button:hover {
  background: linear-gradient(135deg, #388E3C, #4CAF50) !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4) !important;
}

/* Form Elements */
.login-form-title {
  font-size: 2rem !important;
  font-weight: 700 !important;
  margin-bottom: 0.5rem !important;
  text-align: center;
}

.login-form-subtitle {
  font-size: 1rem !important;
  color: #666 !important;
  margin-bottom: 2rem !important;
  text-align: center;
}

.login-brand-badge {
  display: block;
  margin: 0 auto 1.5rem auto;
  padding: 0.5rem 1rem !important;
  border-radius: 20px !important;
  font-size: 0.875rem !important;
  font-weight: 600 !important;
}

.login-form-field {
  margin-bottom: 1.5rem !important;
}

.login-primary-button {
  width: 100% !important;
  padding: 12px 24px !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
  border-radius: 8px !important;
  text-transform: none !important;
  margin-bottom: 1.5rem !important;
  transition: all 0.3s ease !important;
}

.login-features-box {
  background: rgba(0, 0, 0, 0.05) !important;
  border-radius: 8px !important;
  padding: 1rem !important;
  margin-bottom: 1.5rem !important;
}

.login-features-title {
  font-weight: 600 !important;
  margin-bottom: 0.5rem !important;
  font-size: 0.875rem !important;
}

.login-features-list {
  font-size: 0.8rem !important;
  line-height: 1.4 !important;
  color: #666 !important;
}

.login-credentials-box {
  background: rgba(0, 0, 0, 0.03) !important;
  border-radius: 6px !important;
  padding: 0.75rem !important;
  margin-top: 1rem !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

.login-credentials-title {
  font-weight: 600 !important;
  font-size: 0.8rem !important;
  margin-bottom: 0.25rem !important;
}

.login-credentials-text {
  font-size: 0.75rem !important;
  color: #666 !important;
}

.login-upgrade-prompt {
  text-align: center !important;
  margin-top: 1rem !important;
}

/* Language Selector */
.login-language-selector {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

/* Responsive Design */
@media (max-width: 768px) {
  .split-screen-login {
    flex-direction: column;
  }
  
  .login-image-panel {
    min-height: 40vh;
  }
  
  .login-form-panel {
    min-height: 60vh;
    padding: 1rem;
  }
  
  .login-form-container {
    max-width: 100%;
    padding: 1rem;
  }
  
  .login-image-title {
    font-size: 2rem !important;
  }
  
  .login-image-subtitle {
    font-size: 1.2rem !important;
  }
}

@media (max-width: 480px) {
  .login-image-panel {
    min-height: 30vh;
  }
  
  .login-form-panel {
    min-height: 70vh;
    padding: 0.5rem;
  }
  
  .login-image-title {
    font-size: 1.5rem !important;
  }
  
  .login-image-subtitle {
    font-size: 1rem !important;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .login-primary-button {
    transition: none !important;
  }
  
  .login-primary-button:hover {
    transform: none !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .login-form-panel {
    background: rgba(255, 255, 255, 0.98) !important;
    -webkit-backdrop-filter: none;
    backdrop-filter: none;
  }

  .login-features-box,
  .login-credentials-box {
    background: rgba(245, 245, 245, 0.95) !important;
    border: 2px solid #000000 !important;
  }
}
