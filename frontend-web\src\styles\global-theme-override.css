/* Global Theme Override - Remove Default Light Theme */

/* Force dark theme globally */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  background: transparent !important;
  color: #ffffff !important;
  font-family: "<PERSON>", "Roboto", "Helvetica", "Arial", sans-serif !important;
}

/* Override any remaining light theme elements */
.MuiPaper-root,
.MuiCard-root,
.MuiDialog-paper,
.MuiDrawer-paper,
.MuiAppBar-root {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: #ffffff !important;
}

/* Text elements */
.MuiTypography-root,
.MuiFormLabel-root,
.MuiInputLabel-root,
.MuiListItemText-primary,
.MuiListItemText-secondary,
.MuiTableCell-root,
.MuiTab-root {
  color: #ffffff !important;
}

/* Input fields */
.MuiOutlinedInput-root,
.MuiFilledInput-root,
.MuiInput-root {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  color: #ffffff !important;
}

.MuiOutlinedInput-notchedOutline {
  border-color: rgba(255, 255, 255, 0.3) !important;
}

.MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline {
  border-color: rgba(255, 255, 255, 0.5) !important;
}

.MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: var(--primary-color, #2E7D32) !important;
  box-shadow: 0 0 0 3px var(--primary-color-alpha, rgba(46, 125, 50, 0.3)) !important;
}

/* Buttons */
.MuiButton-root {
  border-radius: 12px !important;
  text-transform: none !important;
  font-weight: 600 !important;
  color: #ffffff !important;
}

.MuiButton-contained {
  background: var(--primary-gradient, linear-gradient(135deg, #2E7D32 0%, #4CAF50 50%, #81C784 100%)) !important;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.MuiButton-outlined {
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  color: #ffffff !important;
}

.MuiButton-text {
  color: #ffffff !important;
}

/* Lists and menus */
.MuiList-root,
.MuiMenu-paper,
.MuiPopover-paper {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.MuiMenuItem-root {
  color: #ffffff !important;
}

.MuiMenuItem-root:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

.MuiMenuItem-root.Mui-selected {
  background: var(--primary-color, #2E7D32) !important;
}

/* Tables */
.MuiTableContainer-root {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
}

.MuiTable-root {
  background: transparent !important;
}

.MuiTableHead-root {
  background: rgba(255, 255, 255, 0.1) !important;
}

.MuiTableRow-root:hover {
  background: rgba(255, 255, 255, 0.05) !important;
}

/* Tabs */
.MuiTabs-root {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
}

.MuiTab-root {
  color: rgba(255, 255, 255, 0.8) !important;
}

.MuiTab-root.Mui-selected {
  color: #ffffff !important;
}

.MuiTabs-indicator {
  background: var(--primary-color, #2E7D32) !important;
}

/* Chips */
.MuiChip-root {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Alerts and notifications */
.MuiAlert-root {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  color: #ffffff !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Progress indicators */
.MuiLinearProgress-root {
  background: rgba(255, 255, 255, 0.1) !important;
}

.MuiLinearProgress-bar {
  background: var(--primary-color, #2E7D32) !important;
}

.MuiCircularProgress-root {
  color: var(--primary-color, #2E7D32) !important;
}

/* Switches and checkboxes */
.MuiSwitch-root .MuiSwitch-track {
  background: rgba(255, 255, 255, 0.3) !important;
}

.MuiSwitch-root .Mui-checked + .MuiSwitch-track {
  background: var(--primary-color, #2E7D32) !important;
}

.MuiCheckbox-root {
  color: rgba(255, 255, 255, 0.8) !important;
}

.MuiCheckbox-root.Mui-checked {
  color: var(--primary-color, #2E7D32) !important;
}

/* Radio buttons */
.MuiRadio-root {
  color: rgba(255, 255, 255, 0.8) !important;
}

.MuiRadio-root.Mui-checked {
  color: var(--primary-color, #2E7D32) !important;
}

/* Tooltips */
.MuiTooltip-tooltip {
  background: rgba(0, 0, 0, 0.9) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Scrollbars */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-color, #2E7D32);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color-dark, #1B5E20);
}

/* Custom AgriIntel components */
.agri-card,
.module-card,
.dashboard-card {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 16px !important;
  color: #ffffff !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.agri-card:hover,
.module-card:hover,
.dashboard-card:hover {
  transform: translateY(-4px) !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3) !important;
  background: rgba(255, 255, 255, 0.15) !important;
}

/* Remove any white backgrounds */
.MuiContainer-root,
.MuiGrid-root,
.MuiBox-root {
  background: transparent !important;
}

/* Ensure text is always visible */
h1, h2, h3, h4, h5, h6, p, span, div, label {
  color: #ffffff !important;
}

/* Override any remaining light theme elements */
[class*="light"],
[class*="Light"] {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;
}

/* CSS Variables for dynamic theming */
:root {
  --primary-color: #2E7D32;
  --primary-color-light: #4CAF50;
  --primary-color-dark: #1B5E20;
  --primary-color-alpha: rgba(46, 125, 50, 0.3);
  --secondary-color: #FF8F00;
  --primary-gradient: linear-gradient(135deg, #2E7D32 0%, #4CAF50 50%, #81C784 100%);
}
