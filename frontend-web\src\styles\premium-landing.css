/**
 * Aqua Shatar Inspired Landing Page Styles
 * Modern, Professional SaaS Design for AgriIntel
 */

/* Global Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #1a202c;
  overflow-x: hidden;
}

/* Aqua Hero Section */
.aqua-hero {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  display: flex;
  align-items: center;
  padding: 120px 0 80px;
}

.aqua-hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

/* Hero Content */
.aqua-hero-content {
  z-index: 10;
}

.aqua-hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 50px;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
}

.aqua-badge-icon {
  font-size: 16px;
}

.aqua-hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 900;
  line-height: 1.1;
  color: #1a202c;
  margin-bottom: 24px;
  letter-spacing: -0.02em;
}

.aqua-hero-highlight {
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.aqua-hero-description {
  font-size: 1.2rem;
  color: #4a5568;
  margin-bottom: 32px;
  line-height: 1.7;
  max-width: 500px;
}

.aqua-hero-features {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 40px;
}

.aqua-feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #2d3748;
  font-weight: 500;
}

.aqua-feature-icon {
  color: #4CAF50;
  font-size: 20px;
}

.aqua-hero-buttons {
  display: flex;
  gap: 16px;
  margin-bottom: 48px;
  flex-wrap: wrap;
}

.aqua-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 16px 32px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.aqua-btn-primary {
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
  color: white;
  box-shadow: 0 8px 30px rgba(76, 175, 80, 0.3);
}

.aqua-btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 40px rgba(76, 175, 80, 0.4);
}

.aqua-btn-secondary {
  background: white;
  color: #4CAF50;
  border: 2px solid #4CAF50;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.aqua-btn-secondary:hover {
  background: #4CAF50;
  color: white;
  transform: translateY(-3px);
}

.aqua-hero-stats {
  display: flex;
  gap: 32px;
  flex-wrap: wrap;
}

.aqua-stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.aqua-stat-icon {
  font-size: 24px;
}

.aqua-stat-content {
  display: flex;
  flex-direction: column;
}

.aqua-stat-number {
  font-size: 1.5rem;
  font-weight: 900;
  color: #1a202c;
  line-height: 1;
}

.aqua-stat-label {
  font-size: 0.9rem;
  color: #718096;
  font-weight: 500;
}

/* Hero Image Section */
.aqua-hero-image {
  position: relative;
  z-index: 5;
}

.aqua-image-container {
  position: relative;
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.aqua-hero-main-image {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 24px;
}

/* Floating Cards */
.aqua-floating-card {
  position: absolute;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 16px;
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.aqua-card-1 {
  top: 20%;
  left: -20%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
}

.aqua-card-2 {
  top: 60%;
  right: -15%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
}

.aqua-card-3 {
  bottom: 10%;
  left: -15%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
}

.aqua-card-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
  border-radius: 12px;
  color: white;
}

.aqua-card-content {
  display: flex;
  flex-direction: column;
}

.aqua-card-number {
  font-size: 1.5rem;
  font-weight: 900;
  color: #1a202c;
  line-height: 1;
}

.aqua-card-label {
  font-size: 0.9rem;
  color: #718096;
  font-weight: 500;
}

/* Section Headers */
.aqua-section-header {
  text-align: center;
  margin-bottom: 80px;
}

.aqua-section-title {
  font-size: clamp(2.5rem, 4vw, 3.5rem);
  font-weight: 900;
  color: #1a202c;
  margin-bottom: 20px;
  line-height: 1.2;
}

.aqua-section-subtitle {
  font-size: 1.2rem;
  color: #4a5568;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Testimonials Section */
.aqua-testimonials {
  padding: 120px 0;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
}

.aqua-testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
}

.aqua-testimonial-card {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
}

.aqua-testimonial-card:hover {
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.aqua-testimonial-rating {
  display: flex;
  gap: 4px;
  margin-bottom: 20px;
}

.aqua-star-icon {
  color: #FFC107;
  font-size: 20px;
}

.aqua-testimonial-quote {
  font-size: 1.1rem;
  color: #2d3748;
  line-height: 1.7;
  margin-bottom: 24px;
  font-style: italic;
}

.aqua-testimonial-author {
  display: flex;
  align-items: center;
  gap: 16px;
}

.aqua-author-image {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

.aqua-author-info {
  display: flex;
  flex-direction: column;
}

.aqua-author-name {
  font-weight: 700;
  color: #1a202c;
  font-size: 1rem;
}

.aqua-author-role {
  color: #718096;
  font-size: 0.9rem;
}

/* Gallery Section */
.aqua-gallery {
  padding: 120px 0;
  background: white;
}

.aqua-gallery-tabs {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 60px;
  flex-wrap: wrap;
}

.aqua-tab {
  padding: 12px 24px;
  border: 2px solid rgba(46, 125, 50, 0.3);
  background: linear-gradient(135deg,
    rgba(46, 125, 50, 0.1) 0%,
    rgba(21, 101, 192, 0.08) 100%);
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  color: #2E7D32;
  border-radius: 50px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.aqua-tab.active,
.aqua-tab:hover {
  background: #4CAF50;
  color: white;
  border-color: #4CAF50;
}

.aqua-gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.aqua-gallery-item {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.aqua-gallery-image {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.aqua-gallery-item:hover .aqua-gallery-image {
  transform: scale(1.05);
}

.aqua-gallery-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 20px;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.aqua-gallery-item:hover .aqua-gallery-overlay {
  transform: translateY(0);
}

.aqua-gallery-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

/* Modern Pricing Section */
.aqua-pricing {
  padding: 120px 0;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
}

.aqua-pricing-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-bottom: 60px;
}

.aqua-toggle-label {
  font-weight: 600;
  color: #4a5568;
}

.aqua-save-badge {
  background: #4CAF50;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  margin-left: 8px;
}

.aqua-toggle-switch {
  position: relative;
  width: 60px;
  height: 30px;
}

.aqua-toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.aqua-toggle-switch label {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #cbd5e0;
  border-radius: 30px;
  transition: 0.3s;
}

.aqua-toggle-switch label:before {
  position: absolute;
  content: "";
  height: 22px;
  width: 22px;
  left: 4px;
  bottom: 4px;
  background: white;
  border-radius: 50%;
  transition: 0.3s;
}

.aqua-toggle-switch input:checked + label {
  background: #4CAF50;
}

.aqua-toggle-switch input:checked + label:before {
  transform: translateX(30px);
}

.aqua-pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.aqua-pricing-card {
  background: linear-gradient(135deg,
    rgba(46, 125, 50, 0.1) 0%,
    rgba(21, 101, 192, 0.08) 50%,
    rgba(46, 125, 50, 0.1) 100%);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 0;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 2px solid rgba(46, 125, 50, 0.2);
  overflow: hidden;
  position: relative;
}

.aqua-pricing-card.popular {
  border-color: #4CAF50;
  transform: scale(1.05);
  box-shadow: 0 20px 60px rgba(76, 175, 80, 0.2);
}

.aqua-pricing-card:hover {
  transform: translateY(-8px);
}

.aqua-pricing-card.popular:hover {
  transform: translateY(-8px) scale(1.05);
}

.aqua-pricing-badge {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
  color: white;
  padding: 12px;
  text-align: center;
  font-weight: 700;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.aqua-badge-icon {
  font-size: 16px;
}

.aqua-pricing-header {
  padding: 40px 40px 20px;
  text-align: center;
}

.aqua-pricing-card.popular .aqua-pricing-header {
  padding-top: 60px;
}

.aqua-pricing-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 24px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: white;
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
  box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
}

.aqua-pricing-name {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 16px;
}

.aqua-pricing-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 16px;
}

.aqua-price-currency {
  font-size: 1.2rem;
  font-weight: 600;
  color: #4a5568;
}

.aqua-price-amount {
  font-size: 3rem;
  font-weight: 900;
  color: #1a202c;
  margin: 0 4px;
}

.aqua-price-period {
  font-size: 1rem;
  color: #718096;
}

.aqua-pricing-description {
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 32px;
}

.aqua-pricing-features {
  padding: 0 40px 40px;
}

.aqua-feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.aqua-feature-check {
  color: #4CAF50;
  font-size: 20px;
  flex-shrink: 0;
}

.aqua-feature-text {
  color: #2d3748;
  font-weight: 500;
}

.aqua-pricing-button {
  margin: 0 40px 40px;
  width: calc(100% - 80px);
  justify-content: center;
  font-size: 1.1rem;
  padding: 18px 32px;
}

.aqua-btn-icon {
  margin-left: 8px;
  font-size: 18px;
}

.aqua-pricing-guarantee {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px 40px;
  background: rgba(76, 175, 80, 0.1);
  color: #2E7D32;
  font-size: 0.9rem;
  font-weight: 600;
}

.aqua-guarantee-icon {
  font-size: 16px;
}

/* Pricing FAQ */
.aqua-pricing-faq {
  margin-top: 80px;
  text-align: center;
}

.aqua-faq-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 40px;
}

.aqua-faq-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  text-align: left;
}

.aqua-faq-item {
  background: white;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.aqua-faq-item h4 {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 12px;
}

.aqua-faq-item p {
  color: #4a5568;
  line-height: 1.6;
  margin: 0;
}

/* Footer Styles */
.aqua-footer {
  background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  color: white;
  padding: 80px 0 0;
}

.aqua-footer-main {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 60px;
  margin-bottom: 60px;
}

.aqua-footer-brand {
  max-width: 400px;
}

.aqua-footer-logo {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
}

.aqua-logo-icon {
  font-size: 32px;
  color: #4CAF50;
}

.aqua-logo-text {
  font-size: 1.8rem;
  font-weight: 900;
  color: white;
}

.aqua-footer-description {
  color: #a0aec0;
  line-height: 1.7;
  margin-bottom: 32px;
}

.aqua-footer-social {
  display: flex;
  gap: 16px;
}

.aqua-social-link {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
}

.aqua-social-link:hover {
  background: #4CAF50;
  transform: translateY(-3px);
}

.aqua-footer-contact {
  text-align: left;
}

.aqua-footer-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: white;
  margin-bottom: 24px;
}

.aqua-contact-info {
  margin-bottom: 32px;
}

.aqua-contact-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  color: #a0aec0;
}

.aqua-contact-icon {
  color: #4CAF50;
  font-size: 20px;
}

.aqua-footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 30px 0;
}

.aqua-footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.aqua-footer-copyright {
  color: #a0aec0;
}

.aqua-footer-legal {
  display: flex;
  gap: 24px;
}

.aqua-footer-legal a {
  color: #a0aec0;
  text-decoration: none;
  transition: color 0.3s ease;
}

.aqua-footer-legal a:hover {
  color: #4CAF50;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .aqua-hero-container {
    grid-template-columns: 1fr;
    gap: 60px;
    text-align: center;
  }

  .aqua-hero-stats {
    justify-content: center;
  }

  .aqua-floating-card {
    display: none;
  }

  .aqua-footer-main {
    grid-template-columns: 1fr;
    gap: 40px;
  }
}

@media (max-width: 768px) {
  .aqua-hero {
    padding: 100px 0 60px;
  }

  .aqua-hero-container {
    padding: 0 20px;
    gap: 40px;
  }

  .aqua-hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .aqua-btn {
    width: 100%;
    max-width: 300px;
  }

  .aqua-testimonials-grid,
  .aqua-gallery-grid,
  .aqua-pricing-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .aqua-pricing-card.popular {
    transform: none;
  }

  .aqua-pricing-card.popular:hover {
    transform: translateY(-8px);
  }

  .aqua-gallery-tabs {
    gap: 4px;
  }

  .aqua-tab {
    padding: 8px 16px;
    font-size: 0.9rem;
  }

  .aqua-footer-bottom-content {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .aqua-hero-container {
    padding: 0 16px;
  }

  .aqua-hero-title {
    font-size: 2rem;
  }

  .aqua-hero-description {
    font-size: 1rem;
  }

  .aqua-hero-stats {
    flex-direction: column;
    gap: 20px;
  }

  .aqua-section-title {
    font-size: 2rem;
  }

  .aqua-testimonial-card,
  .aqua-pricing-header,
  .aqua-pricing-features {
    padding: 30px 20px;
  }

  .aqua-pricing-button {
    margin: 0 20px 30px;
    width: calc(100% - 40px);
  }
}

/* Container */
.premium-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

@media (max-width: 768px) {
  .premium-container {
    padding: 0 20px;
  }
}

@media (max-width: 480px) {
  .premium-container {
    padding: 0 16px;
  }
}

.premium-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  z-index: 1;
}

/* Animated Background Particles */
.premium-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  pointer-events: none;
}

.premium-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: linear-gradient(45deg, #00d4ff, #5b73ff);
  border-radius: 50%;
  animation: float-particle 15s infinite linear;
  opacity: 0.6;
}

.premium-particle:nth-child(odd) {
  background: linear-gradient(45deg, #ff6b6b, #ffa726);
  animation-duration: 20s;
}

.premium-particle:nth-child(3n) {
  background: linear-gradient(45deg, #4ecdc4, #44a08d);
  animation-duration: 25s;
}

@keyframes float-particle {
  0% {
    transform: translateY(100vh) translateX(0) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.6;
  }
  90% {
    opacity: 0.6;
  }
  100% {
    transform: translateY(-100px) translateX(100px) rotate(360deg);
    opacity: 0;
  }
}

/* Premium Navigation */
.premium-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(10, 14, 39, 0.95);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.premium-nav.scrolled {
  background: rgba(10, 14, 39, 0.98);
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.3);
}

.premium-nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
}

.premium-logo {
  font-size: 2rem;
  font-weight: 900;
  background: linear-gradient(135deg, #00d4ff 0%, #5b73ff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-decoration: none;
}

.premium-nav-links {
  display: flex;
  gap: 40px;
  list-style: none;
  margin: 0;
  padding: 0;
}

.premium-nav-link {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.premium-nav-link:hover {
  color: #00d4ff;
}

.premium-nav-link::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #00d4ff, #5b73ff);
  transition: width 0.3s ease;
}

.premium-nav-link:hover::after {
  width: 100%;
}

.premium-nav-actions {
  display: flex;
  gap: 12px;
}

.premium-hero-title-gradient {
  background: linear-gradient(135deg, #00d4ff 0%, #5b73ff 50%, #ff6b6b 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.premium-hero-stat-number {
  font-size: 2.5rem;
  font-weight: 900;
  background: linear-gradient(135deg, #00d4ff 0%, #5b73ff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 8px;
}

.premium-hero-stat-label {
  font-size: 1rem;
  opacity: 0.8;
}

/* Premium Hero Content */
.premium-hero-content {
  position: relative;
  z-index: 10;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  text-align: center;
}

.premium-hero-title {
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: 900;
  line-height: 1.1;
  margin-bottom: 24px;
  background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 50%, #c7d2fe 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.premium-hero-subtitle {
  font-size: clamp(1.2rem, 3vw, 1.8rem);
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 40px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.premium-hero-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 60px;
}

.premium-btn {
  padding: 16px 32px;
  border: none;
  border-radius: 50px;
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.premium-btn-primary {
  background: linear-gradient(135deg, #00d4ff 0%, #5b73ff 100%);
  color: white;
  box-shadow: 0 8px 30px rgba(0, 212, 255, 0.3);
}

.premium-btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 40px rgba(0, 212, 255, 0.4);
}

.premium-btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.premium-btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-3px);
}

/* Premium Features Section */
.premium-features {
  padding: 120px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
}

.premium-features::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(180deg, #4e5f94 0%, transparent 100%);
}

.premium-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.premium-section-title {
  text-align: center;
  margin-bottom: 80px;
}

.premium-section-title h2 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 900;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.premium-section-title p {
  font-size: 1.2rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.premium-features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
  margin-bottom: 80px;
}

.premium-feature-card {
  background: rgba(255, 255, 255, 0.8);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 24px;
  padding: 40px;
  text-align: center;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.premium-feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #00d4ff, #5b73ff, #ff6b6b);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.premium-feature-card:hover {
  transform: translateY(-12px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.95);
}

.premium-feature-card:hover::before {
  opacity: 1;
}

.premium-feature-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 24px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #00d4ff 0%, #5b73ff 100%);
  color: white;
  font-size: 32px;
  box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
}

.premium-feature-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 16px;
  color: #1e293b;
}

.premium-feature-description {
  color: #64748b;
  line-height: 1.6;
  font-size: 1rem;
}

/* Premium Stats Section */
.premium-stats {
  padding: 100px 0;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.premium-stats::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.3;
}

.premium-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  position: relative;
  z-index: 2;
}

.premium-stat-card {
  text-align: center;
  padding: 40px 20px;
  background: rgba(255, 255, 255, 0.05);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  transition: all 0.3s ease;
}

.premium-stat-card:hover {
  transform: translateY(-8px);
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

.premium-stat-number {
  font-size: 3rem;
  font-weight: 900;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #00d4ff 0%, #5b73ff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.premium-stat-label {
  font-size: 1.1rem;
  color: #cbd5e1;
  font-weight: 500;
}

/* Premium CTA Section */
.premium-cta {
  padding: 120px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  text-align: center;
}

.premium-cta-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 900;
  margin-bottom: 24px;
  background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.premium-cta-description {
  font-size: 1.2rem;
  color: #64748b;
  margin-bottom: 40px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.premium-cta-button {
  font-size: 1.2rem;
  padding: 20px 40px;
}

/* Premium Pricing Section */
.premium-pricing {
  padding: 120px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.premium-pricing-header {
  text-align: center;
  margin-bottom: 80px;
}

.premium-pricing-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 900;
  margin-bottom: 24px;
  background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.premium-pricing-subtitle {
  font-size: 1.2rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
}

.premium-pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.premium-pricing-card {
  background: rgba(255, 255, 255, 0.9);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 24px;
  padding: 40px;
  text-align: center;
  position: relative;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.premium-pricing-card.popular {
  border: 2px solid #3b82f6;
  box-shadow: 0 20px 60px rgba(59, 130, 246, 0.2);
  transform: scale(1.05);
}

.premium-pricing-card:hover {
  transform: translateY(-12px);
}

.premium-pricing-card.popular:hover {
  transform: translateY(-12px) scale(1.05);
}

.premium-pricing-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  padding: 8px 24px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
}

.premium-pricing-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 24px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.premium-pricing-icon.beta {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.premium-pricing-icon.professional {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.premium-pricing-icon.enterprise {
  background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
}

.premium-pricing-name {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 8px;
  color: #1e293b;
}

.premium-pricing-price {
  margin-bottom: 16px;
}

.premium-pricing-price-amount {
  font-size: 3rem;
  font-weight: 900;
}

.premium-pricing-price-amount.beta {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.premium-pricing-price-amount.professional {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.premium-pricing-price-amount.enterprise {
  background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.premium-pricing-duration {
  font-size: 1rem;
  color: #64748b;
  margin-left: 8px;
}

.premium-pricing-description {
  color: #64748b;
  margin-bottom: 32px;
  line-height: 1.6;
}

.premium-pricing-features {
  list-style: none;
  padding: 0;
  margin-bottom: 32px;
  text-align: left;
}

.premium-pricing-feature {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  color: #374151;
}

.premium-pricing-feature-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 12px;
  color: white;
}

.premium-pricing-feature-icon.beta {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.premium-pricing-feature-icon.professional {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.premium-pricing-feature-icon.enterprise {
  background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
}

.premium-pricing-button {
  width: 100%;
  border: none;
}

.premium-pricing-button.beta {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.premium-pricing-button.professional {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.premium-pricing-button.enterprise {
  background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
}

/* Premium Footer */
.premium-footer {
  background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 100%);
  color: white;
  padding: 60px 0 30px;
  text-align: center;
}

.premium-footer-logo {
  font-size: 2rem;
  font-weight: 900;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #00d4ff 0%, #5b73ff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.premium-footer-tagline {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 30px;
}

.premium-footer-links {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
  margin-bottom: 30px;
}

.premium-footer-link {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: color 0.3s ease;
}

.premium-footer-link:hover {
  color: #00d4ff;
}

.premium-footer-copyright {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .premium-nav-links {
    display: none;
  }

  .premium-hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .premium-btn {
    width: 100%;
    max-width: 280px;
    justify-content: center;
  }

  .premium-features-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .premium-feature-card {
    padding: 30px 20px;
  }

  .premium-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .premium-stat-card {
    padding: 30px 15px;
  }

  .premium-footer-links {
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .premium-stats-grid {
    grid-template-columns: 1fr;
  }

  .premium-footer-links {
    flex-direction: column;
    gap: 15px;
  }
}
