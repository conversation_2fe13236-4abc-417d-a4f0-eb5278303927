/**
 * Image Optimization Script for AgriIntel Landing Page Backgrounds
 * Optimizes the downloaded stock images for better web performance
 */

const fs = require('fs');
const path = require('path');

// Image optimization configuration
const imageConfig = {
  quality: 85,
  maxWidth: 1920,
  maxHeight: 1080,
  formats: ['webp', 'jpg'],
  sizes: [
    { width: 1920, height: 1080, suffix: '-desktop' },
    { width: 1024, height: 768, suffix: '-tablet' },
    { width: 768, height: 576, suffix: '-mobile' }
  ]
};

// Landing page background images
const landingImages = [
  {
    filename: 'high-tech-pasture.jpg',
    alt: 'High-technology pasture grass system with mobile technology for livestock ranch',
    tab: 'home'
  },
  {
    filename: 'agricultural-technology.jpg',
    alt: 'Modern agricultural technology and smart farming solutions',
    tab: 'features'
  },
  {
    filename: 'iot-agriculture.png',
    alt: 'IoT Agriculture technology with connected farming systems',
    tab: 'testimonials'
  },
  {
    filename: 'smart-farming-tech.png',
    alt: 'Smart farming technology and precision agriculture',
    tab: 'pricing'
  },
  {
    filename: 'environmental-assessment.jpg',
    alt: 'Environmental assessment and sustainable farming practices',
    tab: 'trust-security'
  },
  {
    filename: 'smart-farming-infographics.jpg',
    alt: 'Smart farming infographics with precision agriculture icons',
    tab: 'faq'
  },
  {
    filename: 'iot-in-agriculture.png',
    alt: 'IoT in Agriculture with connected devices and data analytics',
    tab: 'contact'
  }
];

/**
 * Check if images exist and get their file sizes
 */
function checkImageFiles() {
  const imagesDir = path.join(__dirname, '../public/images/landing-backgrounds');
  const results = [];
  
  console.log('🔍 Checking landing page background images...\n');
  
  landingImages.forEach((image, index) => {
    const imagePath = path.join(imagesDir, image.filename);
    
    if (fs.existsSync(imagePath)) {
      const stats = fs.statSync(imagePath);
      const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
      
      results.push({
        ...image,
        path: imagePath,
        size: stats.size,
        sizeInMB: sizeInMB,
        exists: true
      });
      
      console.log(`✅ ${image.filename}`);
      console.log(`   Tab: ${image.tab}`);
      console.log(`   Size: ${sizeInMB} MB`);
      console.log(`   Alt: ${image.alt}\n`);
    } else {
      results.push({
        ...image,
        exists: false
      });
      
      console.log(`❌ ${image.filename} - File not found\n`);
    }
  });
  
  return results;
}

/**
 * Generate CSS for responsive background images
 */
function generateResponsiveCSS() {
  console.log('📝 Generating responsive CSS for background images...\n');
  
  let css = `/* Responsive Background Images for Landing Page Tabs */\n\n`;
  
  // Add media queries for different screen sizes
  css += `/* Desktop and large screens */\n`;
  css += `@media (min-width: 1200px) {\n`;
  landingImages.forEach((image, index) => {
    css += `  .MuiBox-root[role="tabpanel"]:nth-of-type(${index + 1}) {\n`;
    css += `    background-image: url('/images/landing-backgrounds/${image.filename}');\n`;
    css += `  }\n`;
  });
  css += `}\n\n`;
  
  css += `/* Tablet screens */\n`;
  css += `@media (min-width: 768px) and (max-width: 1199px) {\n`;
  landingImages.forEach((image, index) => {
    css += `  .MuiBox-root[role="tabpanel"]:nth-of-type(${index + 1}) {\n`;
    css += `    background-image: url('/images/landing-backgrounds/${image.filename}');\n`;
    css += `    background-size: cover;\n`;
    css += `    background-position: center;\n`;
    css += `  }\n`;
  });
  css += `}\n\n`;
  
  css += `/* Mobile screens */\n`;
  css += `@media (max-width: 767px) {\n`;
  landingImages.forEach((image, index) => {
    css += `  .MuiBox-root[role="tabpanel"]:nth-of-type(${index + 1}) {\n`;
    css += `    background-image: url('/images/landing-backgrounds/${image.filename}');\n`;
    css += `    background-size: cover;\n`;
    css += `    background-position: center;\n`;
    css += `    background-attachment: scroll;\n`;
    css += `  }\n`;
  });
  css += `}\n\n`;
  
  return css;
}

/**
 * Generate image preload links for better performance
 */
function generatePreloadLinks() {
  console.log('🚀 Generating preload links for critical images...\n');
  
  let preloadLinks = '';
  
  landingImages.forEach((image, index) => {
    if (index < 3) { // Preload first 3 images (Home, Features, Testimonials)
      preloadLinks += `<link rel="preload" as="image" href="/images/landing-backgrounds/${image.filename}" />\n`;
    }
  });
  
  return preloadLinks;
}

/**
 * Main optimization function
 */
function optimizeLandingImages() {
  console.log('🎨 AgriIntel Landing Page Image Optimization\n');
  console.log('='.repeat(50) + '\n');
  
  // Check image files
  const imageResults = checkImageFiles();
  
  // Count existing images
  const existingImages = imageResults.filter(img => img.exists);
  const totalSize = existingImages.reduce((sum, img) => sum + img.size, 0);
  const totalSizeInMB = (totalSize / (1024 * 1024)).toFixed(2);
  
  console.log(`📊 Summary:`);
  console.log(`   Total images: ${existingImages.length}/${landingImages.length}`);
  console.log(`   Total size: ${totalSizeInMB} MB\n`);
  
  // Generate responsive CSS
  const responsiveCSS = generateResponsiveCSS();
  
  // Generate preload links
  const preloadLinks = generatePreloadLinks();
  
  // Save optimization results
  const outputDir = path.join(__dirname, '../public/images/landing-backgrounds');
  
  // Save responsive CSS
  fs.writeFileSync(
    path.join(outputDir, 'responsive-backgrounds.css'),
    responsiveCSS
  );
  
  // Save preload links
  fs.writeFileSync(
    path.join(outputDir, 'preload-links.html'),
    preloadLinks
  );
  
  console.log('✅ Optimization complete!');
  console.log(`   Responsive CSS saved to: responsive-backgrounds.css`);
  console.log(`   Preload links saved to: preload-links.html\n`);
  
  // Performance recommendations
  console.log('💡 Performance Recommendations:');
  existingImages.forEach(img => {
    if (parseFloat(img.sizeInMB) > 1) {
      console.log(`   ⚠️  ${img.filename} is ${img.sizeInMB} MB - consider compression`);
    }
  });
  
  console.log('\n🎯 Next Steps:');
  console.log('   1. Review generated responsive CSS');
  console.log('   2. Add preload links to HTML head');
  console.log('   3. Test image loading performance');
  console.log('   4. Consider WebP format for better compression\n');
}

// Run optimization if called directly
if (require.main === module) {
  optimizeLandingImages();
}

module.exports = {
  optimizeLandingImages,
  checkImageFiles,
  generateResponsiveCSS,
  generatePreloadLinks,
  landingImages
};
