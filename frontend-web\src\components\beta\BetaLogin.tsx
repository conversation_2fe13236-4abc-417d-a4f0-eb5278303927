import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import {
  Ty<PERSON>graphy,
  TextField,
  Button,
  Alert,
  InputAdornment,
  IconButton,
  CircularProgress,
  Box,
  Card,
  CardContent,
  Container,
  Chip,
  Stack
} from '@mui/material';
import {
  Person,
  Lock,
  Visibility,
  VisibilityOff,
  Agriculture,
  Star,
  TrendingUp
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import EnhancedLanguageSelector from '../common/EnhancedLanguageSelector';
import '../../styles/beta-tier.css';

const BetaLogin: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');

  const { login, isLoading, error: authError } = useAuth();
  const { translate } = useLanguage();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (authError) {
      setError(authError);
    }
  }, [authError]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!username || !password) {
      setError('Please enter both username and password');
      return;
    }
    try {
      await login(username, password);
      
      // Get user from localStorage after successful login
      const userData = JSON.parse(localStorage.getItem('user') || '{}');
      const redirectPath = location.state?.from?.pathname || '/beta-dashboard';

      // Redirect to BETA dashboard for BETA users
      if (userData.role === 'beta') {
        navigate('/beta-dashboard', { replace: true });
      } else {
        navigate(redirectPath, { replace: true });
      }
    } catch (err) {
      setError('Invalid credentials. Please try again.');
    }
  };

  const handleTogglePassword = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="beta-login-container">
      {/* Language Selector */}
      <Box sx={{ position: 'absolute', top: 20, right: 20, zIndex: 1000 }}>
        <EnhancedLanguageSelector
          variant="futuristic"
          showLabel={false}
          showFlag={true}
          size="medium"
        />
      </Box>

      <Container maxWidth="sm" sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        py: 4,
        position: 'relative',
        zIndex: 1
      }}>
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          style={{ width: '100%' }}
        >
          <Card className="beta-login-card">
            <CardContent sx={{ p: 4 }}>
              {/* BETA Branding */}
              <Box sx={{ textAlign: 'center', mb: 3 }}>
                <Chip 
                  label="BETA V1 Starter" 
                  className="beta-brand-badge"
                  icon={<Star />}
                />
                <Typography variant="h4" className="beta-login-title">
                  Welcome to AgriIntel
                </Typography>
                <Typography variant="body1" className="beta-login-subtitle">
                  Start your free trial with our BETA V1 Starter package
                </Typography>
              </Box>

              {/* BETA Features Preview */}
              <Box sx={{ mb: 3, p: 2, bgcolor: 'rgba(255, 152, 0, 0.1)', borderRadius: 2 }}>
                <Typography variant="h6" sx={{ color: '#E65100', fontWeight: 600, mb: 1 }}>
                  🎯 BETA V1 Includes:
                </Typography>
                <Stack spacing={0.5}>
                  <Typography variant="body2" sx={{ color: '#E65100' }}>
                    • Animal Management (up to 50 animals)
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#E65100' }}>
                    • Basic Health Monitoring
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#E65100' }}>
                    • Feed Recording & Tracking
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#E65100' }}>
                    • Financial Overview
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#E65100' }}>
                    • Basic Settings & Configuration
                  </Typography>
                </Stack>
              </Box>

              {/* Login Form */}
              <Box component="form" onSubmit={handleLogin} sx={{ width: '100%' }}>
                {error && (
                  <Alert severity="error" sx={{ mb: 2 }}>
                    {error}
                  </Alert>
                )}

                <TextField
                  fullWidth
                  label="Username"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  margin="normal"
                  required
                  autoComplete="username"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Person sx={{ color: '#FF9800' }} />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      '&:hover fieldset': {
                        borderColor: '#FF9800',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#FF9800',
                      },
                    },
                    '& .MuiInputLabel-root.Mui-focused': {
                      color: '#FF9800',
                    },
                  }}
                />

                <TextField
                  fullWidth
                  label="Password"
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  margin="normal"
                  required
                  autoComplete="current-password"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Lock sx={{ color: '#FF9800' }} />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle password visibility"
                          onClick={handleTogglePassword}
                          edge="end"
                          sx={{ color: '#FF9800' }}
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      '&:hover fieldset': {
                        borderColor: '#FF9800',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#FF9800',
                      },
                    },
                    '& .MuiInputLabel-root.Mui-focused': {
                      color: '#FF9800',
                    },
                  }}
                />

                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  disabled={isLoading}
                  className="beta-login-button"
                  sx={{ mt: 3, mb: 2, py: 1.5 }}
                  startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : <Agriculture />}
                >
                  {isLoading ? 'Signing In...' : 'Start Free Trial'}
                </Button>
              </Box>

              {/* Demo Credentials */}
              <Box sx={{ mt: 3, p: 2, bgcolor: '#FFF3E0', borderRadius: 2, border: '1px solid #FFB74D' }}>
                <Typography variant="body2" sx={{ color: '#E65100', fontWeight: 600, mb: 1 }}>
                  🔑 Demo Credentials:
                </Typography>
                <Typography variant="body2" sx={{ color: '#E65100' }}>
                  Username: <strong>Demo</strong> | Password: <strong>123</strong>
                </Typography>
              </Box>

              {/* Upgrade Prompt */}
              <Box sx={{ mt: 3, textAlign: 'center' }}>
                <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
                  Need more features?
                </Typography>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<TrendingUp />}
                  sx={{
                    borderColor: '#FF9800',
                    color: '#FF9800',
                    '&:hover': {
                      borderColor: '#F57C00',
                      backgroundColor: 'rgba(255, 152, 0, 0.1)',
                    },
                  }}
                  onClick={() => navigate('/pricing')}
                >
                  Upgrade to Pro V2 - R699/month
                </Button>
              </Box>
            </CardContent>
          </Card>
        </motion.div>
      </Container>
    </div>
  );
};

export default BetaLogin;
