/**
 * AgriIntel Theme Provider
 * Comprehensive theme system with gradient themes and livestock branding
 */

import React, { createContext, useContext, useState, useEffect } from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import '../../styles/components/theme-selector.css';

// Enhanced Theme types with more diversity
export type ThemeVariant = 'nature' | 'earth' | 'sky' | 'sunset' | 'forest' | 'professional' | 'warm' | 'modern' | 'ocean' | 'crimson' | 'purple' | 'teal';
export type ThemeMode = 'light' | 'dark' | 'auto';

interface AgriIntelTheme {
  variant: ThemeVariant;
  mode: ThemeMode;
  gradients: Record<string, string>;
  colors: Record<string, string>;
}

interface ThemeContextType {
  theme: AgriIntelTheme;
  setThemeVariant: (variant: ThemeVariant) => void;
  setThemeMode: (mode: ThemeMode) => void;
  toggleMode: () => void;
}

// Enhanced Theme configurations with diverse color palettes
const themeVariants: Record<ThemeVariant, any> = {
  // Nature Themes
  nature: {
    primary: '#2E7D32',
    primaryLight: '#4CAF50',
    primaryDark: '#1B5E20',
    secondary: '#FF8F00',
    gradient: 'linear-gradient(135deg, #2E7D32 0%, #4CAF50 50%, #81C784 100%)',
    name: 'Nature Green',
    category: 'Nature'
  },
  forest: {
    primary: '#388E3C',
    primaryLight: '#66BB6A',
    primaryDark: '#2E7D32',
    secondary: '#8BC34A',
    gradient: 'linear-gradient(135deg, #388E3C 0%, #66BB6A 50%, #A5D6A7 100%)',
    name: 'Forest Green',
    category: 'Nature'
  },
  earth: {
    primary: '#8D6E63',
    primaryLight: '#A1887F',
    primaryDark: '#5D4037',
    secondary: '#FF8F00',
    gradient: 'linear-gradient(135deg, #8D6E63 0%, #A1887F 50%, #BCAAA4 100%)',
    name: 'Earth Brown',
    category: 'Nature'
  },

  // Professional Themes
  professional: {
    primary: '#1565C0',
    primaryLight: '#42A5F5',
    primaryDark: '#0D47A1',
    secondary: '#2E7D32',
    gradient: 'linear-gradient(135deg, #1565C0 0%, #1976D2 50%, #42A5F5 100%)',
    name: 'Professional Blue',
    category: 'Professional'
  },
  sky: {
    primary: '#1976D2',
    primaryLight: '#42A5F5',
    primaryDark: '#0D47A1',
    secondary: '#FF8F00',
    gradient: 'linear-gradient(135deg, #1976D2 0%, #42A5F5 50%, #90CAF9 100%)',
    name: 'Sky Blue',
    category: 'Professional'
  },
  teal: {
    primary: '#00695C',
    primaryLight: '#26A69A',
    primaryDark: '#004D40',
    secondary: '#FF7043',
    gradient: 'linear-gradient(135deg, #00695C 0%, #26A69A 50%, #80CBC4 100%)',
    name: 'Professional Teal',
    category: 'Professional'
  },

  // Warm Themes
  warm: {
    primary: '#F57C00',
    primaryLight: '#FFB74D',
    primaryDark: '#E65100',
    secondary: '#FF5722',
    gradient: 'linear-gradient(135deg, #F57C00 0%, #FF9800 50%, #FFB74D 100%)',
    name: 'Warm Gold',
    category: 'Warm'
  },
  sunset: {
    primary: '#FF8F00',
    primaryLight: '#FFB74D',
    primaryDark: '#E65100',
    secondary: '#2E7D32',
    gradient: 'linear-gradient(135deg, #FF8F00 0%, #FFB74D 50%, #FFCC02 100%)',
    name: 'Sunset Orange',
    category: 'Warm'
  },
  crimson: {
    primary: '#C62828',
    primaryLight: '#EF5350',
    primaryDark: '#B71C1C',
    secondary: '#FF7043',
    gradient: 'linear-gradient(135deg, #C62828 0%, #E53935 50%, #EF5350 100%)',
    name: 'Crimson Red',
    category: 'Warm'
  },

  // Modern Themes
  modern: {
    primary: '#6A1B9A',
    primaryLight: '#AB47BC',
    primaryDark: '#4A148C',
    secondary: '#E91E63',
    gradient: 'linear-gradient(135deg, #6A1B9A 0%, #8E24AA 50%, #AB47BC 100%)',
    name: 'Modern Purple',
    category: 'Modern'
  },
  purple: {
    primary: '#7B1FA2',
    primaryLight: '#BA68C8',
    primaryDark: '#4A148C',
    secondary: '#FF4081',
    gradient: 'linear-gradient(135deg, #7B1FA2 0%, #9C27B0 50%, #BA68C8 100%)',
    name: 'Royal Purple',
    category: 'Modern'
  },
  ocean: {
    primary: '#0277BD',
    primaryLight: '#29B6F6',
    primaryDark: '#01579B',
    secondary: '#00BCD4',
    gradient: 'linear-gradient(135deg, #0277BD 0%, #0288D1 50%, #29B6F6 100%)',
    name: 'Ocean Blue',
    category: 'Modern'
  }
};

// Create theme context
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Theme provider component
export const AgriIntelThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [themeVariant, setThemeVariant] = useState<ThemeVariant>('nature');
  const [themeMode, setThemeMode] = useState<ThemeMode>('light');

  // Load theme from localStorage
  useEffect(() => {
    const savedVariant = localStorage.getItem('agri-theme-variant') as ThemeVariant;
    const savedMode = localStorage.getItem('agri-theme-mode') as ThemeMode;
    
    if (savedVariant && themeVariants[savedVariant]) {
      setThemeVariant(savedVariant);
    }
    
    if (savedMode) {
      setThemeMode(savedMode);
    } else if (savedMode === 'auto') {
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      setThemeMode(prefersDark ? 'dark' : 'light');
    }
  }, []);

  // Save theme to localStorage
  useEffect(() => {
    localStorage.setItem('agri-theme-variant', themeVariant);
    localStorage.setItem('agri-theme-mode', themeMode);
  }, [themeVariant, themeMode]);

  // Create MUI theme
  const muiTheme = createTheme({
    palette: {
      mode: themeMode === 'auto' ? 
        (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : 
        themeMode,
      primary: {
        main: themeVariants[themeVariant].primary,
        light: themeVariants[themeVariant].primaryLight,
        dark: themeVariants[themeVariant].primaryDark,
      },
      secondary: {
        main: themeVariants[themeVariant].secondary,
      },
      background: {
        default: themeMode === 'dark' ? '#121212' : '#f5f5f5',
        paper: themeMode === 'dark' ? '#1e1e1e' : '#ffffff',
      },
    },
    typography: {
      fontFamily: '"Inter", "Roboto", sans-serif',
      h1: {
        fontFamily: '"Poppins", "Inter", sans-serif',
        fontWeight: 700,
      },
      h2: {
        fontFamily: '"Poppins", "Inter", sans-serif',
        fontWeight: 600,
      },
      h3: {
        fontFamily: '"Poppins", "Inter", sans-serif',
        fontWeight: 600,
      },
      h4: {
        fontFamily: '"Poppins", "Inter", sans-serif',
        fontWeight: 500,
      },
      h5: {
        fontFamily: '"Poppins", "Inter", sans-serif',
        fontWeight: 500,
      },
      h6: {
        fontFamily: '"Poppins", "Inter", sans-serif',
        fontWeight: 500,
      },
    },
    shape: {
      borderRadius: 12,
    },
    components: {
      MuiCard: {
        styleOverrides: {
          root: {
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(8px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
            transition: 'all 0.3s ease',
            '&:hover': {
              transform: 'translateY(-4px)',
              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
            },
          },
        },
      },
      MuiButton: {
        styleOverrides: {
          root: {
            borderRadius: 8,
            textTransform: 'none',
            fontWeight: 600,
            transition: 'all 0.3s ease',
            '&:hover': {
              transform: 'translateY(-2px)',
            },
          },
          contained: {
            background: themeVariants[themeVariant].primary,
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            '&:hover': {
              background: themeVariants[themeVariant].primaryDark,
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
            },
          },
        },
      },
      MuiTextField: {
        styleOverrides: {
          root: {
            '& .MuiOutlinedInput-root': {
              borderRadius: 8,
              background: 'rgba(255, 255, 255, 0.9)',
              transition: 'all 0.3s ease',
              '&:hover': {
                background: 'rgba(255, 255, 255, 1)',
              },
              '&.Mui-focused': {
                background: 'rgba(255, 255, 255, 1)',
                boxShadow: `0 0 0 3px ${themeVariants[themeVariant].primary}20`,
              },
            },
          },
        },
      },
      MuiPaper: {
        styleOverrides: {
          root: {
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(8px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
          },
        },
      },
      MuiAppBar: {
        styleOverrides: {
          root: {
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(8px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
          },
        },
      },
    },
  });

  // Theme context value
  const themeContextValue: ThemeContextType = {
    theme: {
      variant: themeVariant,
      mode: themeMode,
      gradients: {
        primary: themeVariants[themeVariant].gradient,
        nature: themeVariants.nature.gradient,
        earth: themeVariants.earth.gradient,
        sky: themeVariants.sky.gradient,
        sunset: themeVariants.sunset.gradient,
        forest: themeVariants.forest.gradient,
      },
      colors: {
        primary: themeVariants[themeVariant].primary,
        primaryLight: themeVariants[themeVariant].primaryLight,
        primaryDark: themeVariants[themeVariant].primaryDark,
        secondary: themeVariants[themeVariant].secondary,
      },
    },
    setThemeVariant: (variant: ThemeVariant) => {
      setThemeVariant(variant);
    },
    setThemeMode: (mode: ThemeMode) => {
      setThemeMode(mode);
    },
    toggleMode: () => {
      setThemeMode(prev => prev === 'light' ? 'dark' : 'light');
    },
  };

  // Apply theme to body
  useEffect(() => {
    const body = document.body;
    body.style.background = themeVariants[themeVariant].gradient;
    body.style.backgroundAttachment = 'fixed';
    body.style.minHeight = '100vh';
    
    // Apply theme class
    body.className = `theme-${themeVariant} mode-${themeMode}`;
    
    return () => {
      body.style.background = '';
      body.style.backgroundAttachment = '';
      body.className = '';
    };
  }, [themeVariant, themeMode]);

  return (
    <ThemeContext.Provider value={themeContextValue}>
      <ThemeProvider theme={muiTheme}>
        <CssBaseline />
        {children}
      </ThemeProvider>
    </ThemeContext.Provider>
  );
};

// Hook to use theme
export const useAgriIntelTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useAgriIntelTheme must be used within an AgriIntelThemeProvider');
  }
  return context;
};

// Enhanced Theme selector component with categories
export const ThemeSelector: React.FC = () => {
  const { theme, setThemeVariant, setThemeMode, toggleMode } = useAgriIntelTheme();

  // Group themes by category
  const themesByCategory = Object.entries(themeVariants).reduce((acc, [key, variant]) => {
    const category = variant.category || 'Other';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push({ key, variant });
    return acc;
  }, {} as Record<string, Array<{ key: string; variant: any }>>);

  return (
    <div className="agri-theme-selector">
      <div className="theme-selector-header">
        <h3>🎨 Theme Customization</h3>
        <p>Choose from our diverse collection of professional themes</p>
      </div>

      {/* Theme Categories */}
      <div className="theme-categories">
        {Object.entries(themesByCategory).map(([category, themes]) => (
          <div key={category} className="theme-category">
            <h4 className="category-title">
              {category === 'Nature' && '🌿'}
              {category === 'Professional' && '💼'}
              {category === 'Warm' && '🔥'}
              {category === 'Modern' && '✨'}
              {category} Themes
            </h4>
            <div className="theme-variants-grid">
              {themes.map(({ key, variant }) => (
                <button
                  key={key}
                  type="button"
                  className={`theme-variant-card ${theme.variant === key ? 'active' : ''}`}
                  onClick={() => setThemeVariant(key as ThemeVariant)}
                  title={`Switch to ${variant.name}`}
                >
                  <div
                    className="theme-preview"
                    style={{ background: variant.gradient }}
                  />
                  <div className="theme-info">
                    <span className="theme-name">{variant.name}</span>
                    <div className="theme-colors">
                      <div
                        className="color-dot"
                        style={{ backgroundColor: variant.primary }}
                      />
                      <div
                        className="color-dot"
                        style={{ backgroundColor: variant.primaryLight }}
                      />
                      <div
                        className="color-dot"
                        style={{ backgroundColor: variant.secondary }}
                      />
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Theme Mode */}
      <div className="theme-mode-section">
        <h4>🌓 Display Mode</h4>
        <div className="theme-modes">
          <button
            type="button"
            className={`theme-mode-btn ${theme.mode === 'light' ? 'active' : ''}`}
            onClick={() => setThemeMode('light')}
            title="Light mode for bright environments"
          >
            ☀️ Light
          </button>
          <button
            type="button"
            className={`theme-mode-btn ${theme.mode === 'dark' ? 'active' : ''}`}
            onClick={() => setThemeMode('dark')}
            title="Dark mode for low light environments"
          >
            🌙 Dark
          </button>
          <button
            type="button"
            className={`theme-mode-btn ${theme.mode === 'auto' ? 'active' : ''}`}
            onClick={() => setThemeMode('auto')}
            title="Automatically adjust based on system preference"
          >
            🕐 Auto
          </button>
        </div>
      </div>
    </div>
  );
};

export default AgriIntelThemeProvider;
