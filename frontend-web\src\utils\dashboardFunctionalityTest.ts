/**
 * Dashboard Functionality Comprehensive Test
 * Tests all BETA module dashboards and components
 */

export interface DashboardTestResult {
  module: string;
  dataLoaded: boolean;
  componentsRendered: boolean;
  interactiveElementsWorking: boolean;
  navigationWorking: boolean;
  issues: string[];
  dataCount: number;
}

export interface DashboardAudit {
  modules: DashboardTestResult[];
  overall: {
    totalModules: number;
    functionalModules: number;
    issuesFound: number;
    successRate: number;
  };
}

/**
 * Test individual module dashboard
 */
function testModuleDashboard(moduleName: string): DashboardTestResult {
  const result: DashboardTestResult = {
    module: moduleName,
    dataLoaded: false,
    componentsRendered: false,
    interactiveElementsWorking: false,
    navigationWorking: false,
    issues: [],
    dataCount: 0
  };

  try {
    // Test data loading
    const dataElements = document.querySelectorAll('[data-testid*="data"], .data-row, .animal-row, .health-record, .feeding-record, .financial-record');
    result.dataCount = dataElements.length;
    result.dataLoaded = dataElements.length > 0;

    if (!result.dataLoaded) {
      result.issues.push('No data elements found - data may not be loading');
    }

    // Test component rendering
    const moduleElements = document.querySelectorAll('.MuiCard-root, .MuiPaper-root, .module-card, .dashboard-card');
    result.componentsRendered = moduleElements.length > 0;

    if (!result.componentsRendered) {
      result.issues.push('No module components rendered');
    }

    // Test interactive elements
    const buttons = document.querySelectorAll('button:not([disabled])');
    const forms = document.querySelectorAll('form, .MuiTextField-root');
    result.interactiveElementsWorking = buttons.length > 0 && forms.length > 0;

    if (buttons.length === 0) {
      result.issues.push('No functional buttons found');
    }
    if (forms.length === 0) {
      result.issues.push('No form elements found');
    }

    // Test navigation
    const navLinks = document.querySelectorAll('a[href], [role="tab"], .nav-link');
    result.navigationWorking = navLinks.length > 0;

    if (!result.navigationWorking) {
      result.issues.push('No navigation elements found');
    }

    // Check for error messages
    const errorElements = document.querySelectorAll('.MuiAlert-standardError, .error-message, [role="alert"]');
    if (errorElements.length > 0) {
      result.issues.push(`${errorElements.length} error messages displayed`);
    }

    // Check for loading states
    const loadingElements = document.querySelectorAll('.MuiCircularProgress-root, .loading, .skeleton');
    if (loadingElements.length > 0) {
      result.issues.push(`${loadingElements.length} loading indicators still active`);
    }

  } catch (error) {
    result.issues.push(`Test execution error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  return result;
}

/**
 * Test BETA module access and restrictions
 */
function testBetaModuleAccess(): any {
  const betaModules = ['animals', 'health', 'feeding', 'financial', 'settings'];
  const restrictedModules = ['breeding', 'inventory', 'commercial', 'analytics', 'compliance'];

  const accessTest = {
    allowedModulesAccessible: 0,
    restrictedModulesBlocked: 0,
    issues: [] as string[]
  };

  // Check if BETA modules are accessible
  betaModules.forEach(module => {
    const moduleElement = document.querySelector(`[data-module="${module}"], .${module}-module, #${module}-module`);
    if (moduleElement) {
      accessTest.allowedModulesAccessible++;
    } else {
      accessTest.issues.push(`BETA module '${module}' not accessible`);
    }
  });

  // Check if restricted modules are properly blocked
  restrictedModules.forEach(module => {
    const moduleElement = document.querySelector(`[data-module="${module}"], .${module}-module, #${module}-module`);
    const upgradePrompt = document.querySelector(`[data-upgrade="${module}"], .upgrade-prompt-${module}`);
    
    if (!moduleElement || upgradePrompt) {
      accessTest.restrictedModulesBlocked++;
    } else {
      accessTest.issues.push(`Restricted module '${module}' is accessible (should be blocked)`);
    }
  });

  return accessTest;
}

/**
 * Test BETA limitations enforcement
 */
function testBetaLimitations(): any {
  const limitations = {
    animalLimitEnforced: false,
    upgradePromptsPresent: false,
    betaBadgeVisible: false,
    issues: [] as string[]
  };

  // Check for 50-animal limit indicators
  const limitIndicators = document.querySelectorAll('[data-testid*="limit"], .limit-indicator, .usage-progress');
  limitations.animalLimitEnforced = limitIndicators.length > 0;

  if (!limitations.animalLimitEnforced) {
    limitations.issues.push('No animal limit indicators found');
  }

  // Check for upgrade prompts
  const upgradeElements = document.querySelectorAll('[data-testid*="upgrade"], .upgrade-prompt, .upgrade-button');
  limitations.upgradePromptsPresent = upgradeElements.length > 0;

  if (!limitations.upgradePromptsPresent) {
    limitations.issues.push('No upgrade prompts found');
  }

  // Check for BETA badges
  const betaBadges = document.querySelectorAll('[data-testid*="beta"], .beta-badge, .beta-chip');
  limitations.betaBadgeVisible = betaBadges.length > 0;

  if (!limitations.betaBadgeVisible) {
    limitations.issues.push('No BETA badges visible');
  }

  return limitations;
}

/**
 * Run comprehensive dashboard functionality test
 */
export async function runDashboardFunctionalityTest(): Promise<DashboardAudit> {
  console.log('🔍 Starting Dashboard Functionality Test...');

  const modules = ['animals', 'health', 'feeding', 'financial', 'settings'];
  const moduleResults: DashboardTestResult[] = [];

  // Test each module
  for (const module of modules) {
    const result = testModuleDashboard(module);
    moduleResults.push(result);
  }

  // Calculate overall statistics
  const functionalModules = moduleResults.filter(m => 
    m.dataLoaded && m.componentsRendered && m.interactiveElementsWorking
  ).length;

  const totalIssues = moduleResults.reduce((sum, m) => sum + m.issues.length, 0);

  const audit: DashboardAudit = {
    modules: moduleResults,
    overall: {
      totalModules: modules.length,
      functionalModules,
      issuesFound: totalIssues,
      successRate: Math.round((functionalModules / modules.length) * 100)
    }
  };

  console.log('✅ Dashboard Functionality Test Complete:', audit.overall);
  return audit;
}

/**
 * Display detailed dashboard test results
 */
export function displayDashboardTestResults(audit: DashboardAudit) {
  console.group('📊 Dashboard Functionality Test Results');

  audit.modules.forEach(module => {
    console.group(`📋 ${module.module.toUpperCase()} Module`);
    console.log(`Data Loaded: ${module.dataLoaded ? '✅' : '❌'} (${module.dataCount} elements)`);
    console.log(`Components Rendered: ${module.componentsRendered ? '✅' : '❌'}`);
    console.log(`Interactive Elements: ${module.interactiveElementsWorking ? '✅' : '❌'}`);
    console.log(`Navigation: ${module.navigationWorking ? '✅' : '❌'}`);
    
    if (module.issues.length > 0) {
      console.log('Issues:');
      module.issues.forEach(issue => console.log(`  ❌ ${issue}`));
    }
    console.groupEnd();
  });

  console.group('📈 Overall Summary');
  console.log(`Total Modules: ${audit.overall.totalModules}`);
  console.log(`Functional Modules: ${audit.overall.functionalModules}`);
  console.log(`Issues Found: ${audit.overall.issuesFound}`);
  console.log(`Success Rate: ${audit.overall.successRate}%`);
  console.groupEnd();

  console.groupEnd();
}

/**
 * Test complete user journey
 */
export async function testCompleteUserJourney(): Promise<any> {
  console.log('🚀 Testing Complete BETA User Journey...');

  const journey = {
    loginAccessible: false,
    dashboardLoads: false,
    modulesAccessible: false,
    dataVisible: false,
    formsWorking: false,
    navigationSmooth: false,
    issues: [] as string[]
  };

  try {
    // Check if we can access login
    journey.loginAccessible = window.location.pathname.includes('login') || 
                             document.querySelector('.login-form, .beta-login') !== null;

    // Check if dashboard loads
    journey.dashboardLoads = document.querySelector('.dashboard, .main-content, .module-container') !== null;

    // Check if modules are accessible
    const moduleElements = document.querySelectorAll('[data-module], .module-card, .module-link');
    journey.modulesAccessible = moduleElements.length >= 5; // Should have at least 5 BETA modules

    // Check if data is visible
    const dataElements = document.querySelectorAll('.data-row, .animal-card, .record-item, .stat-card');
    journey.dataVisible = dataElements.length > 0;

    // Check if forms are working
    const formElements = document.querySelectorAll('form, .MuiTextField-root, input, select');
    journey.formsWorking = formElements.length > 0;

    // Check navigation
    const navElements = document.querySelectorAll('nav, .navigation, .sidebar, .menu');
    journey.navigationSmooth = navElements.length > 0;

    // Collect issues
    if (!journey.loginAccessible) journey.issues.push('Login not accessible');
    if (!journey.dashboardLoads) journey.issues.push('Dashboard not loading');
    if (!journey.modulesAccessible) journey.issues.push('Modules not accessible');
    if (!journey.dataVisible) journey.issues.push('Data not visible');
    if (!journey.formsWorking) journey.issues.push('Forms not working');
    if (!journey.navigationSmooth) journey.issues.push('Navigation issues');

    console.log('🚀 User Journey Test Results:', journey);
    return journey;

  } catch (error) {
    journey.issues.push(`Journey test error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return journey;
  }
}

// Export for global access
(window as any).dashboardTest = {
  runDashboardFunctionalityTest,
  displayDashboardTestResults,
  testBetaModuleAccess,
  testBetaLimitations,
  testCompleteUserJourney
};
