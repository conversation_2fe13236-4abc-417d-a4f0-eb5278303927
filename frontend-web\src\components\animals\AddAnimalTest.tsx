/**
 * Add Animal Test Component
 * Test the enhanced Add Animal form functionality
 */

import React, { useState } from 'react';
import { Button, Container, Typography, Box } from '@mui/material';
import AddAnimalForm from './AddAnimalForm';
import useAgriIntelTheme from '../../hooks/useAgriIntelTheme';

const AddAnimalTest: React.FC = () => {
  const [showForm, setShowForm] = useState(false);
  const [animals, setAnimals] = useState<any[]>([]);
  const { styles } = useAgriIntelTheme();

  const handleAddSuccess = () => {
    // Simulate adding animal to list
    const newAnimal = {
      id: Date.now(),
      tagNumber: `TEST-${Date.now()}`,
      name: 'Test Animal',
      species: 'cattle',
      addedAt: new Date().toLocaleString()
    };
    setAnimals(prev => [...prev, newAnimal]);
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={styles.header}>
        <Typography variant="h4" component="h1" sx={styles.primaryText}>
          🧪 Add Animal Form Test
        </Typography>
        <Typography variant="body1" sx={styles.secondaryText}>
          Test the enhanced Add Animal form with lineage tracking and automatic lifecycle detection
        </Typography>
      </Box>

      <Box sx={{ mb: 4 }}>
        <Button
          variant="contained"
          size="large"
          onClick={() => setShowForm(true)}
          sx={{
            ...styles.primaryButton,
            fontSize: '1.1rem',
            padding: '16px 32px'
          }}
        >
          🐄 Test Add Animal Form
        </Button>
      </Box>

      {animals.length > 0 && (
        <Box sx={styles.card}>
          <Typography variant="h6" sx={{ ...styles.primaryText, mb: 2 }}>
            ✅ Test Results ({animals.length} animals added)
          </Typography>
          {animals.map((animal, index) => (
            <Box
              key={animal.id}
              sx={{
                ...styles.sectionBackground,
                mb: 2,
                p: 2
              }}
            >
              <Typography variant="body1" sx={styles.primaryText}>
                <strong>#{index + 1}</strong> - {animal.tagNumber} ({animal.species})
              </Typography>
              <Typography variant="body2" sx={styles.secondaryText}>
                Added: {animal.addedAt}
              </Typography>
            </Box>
          ))}
        </Box>
      )}

      <Box sx={styles.card}>
        <Typography variant="h6" sx={{ ...styles.primaryText, mb: 2 }}>
          🔬 Test Features
        </Typography>
        <Box component="ul" sx={{ color: '#ffffff', pl: 3 }}>
          <li>✅ Comprehensive form fields (basic info, lineage, health)</li>
          <li>✅ Automatic lifecycle stage calculation (calf → juvenile → adult → retirement)</li>
          <li>✅ Maternal lineage tracking (mother, grandmother)</li>
          <li>✅ Paternal lineage tracking (father, grandfather)</li>
          <li>✅ Breed type selection with South African breeds</li>
          <li>✅ Age input with automatic stage detection</li>
          <li>✅ Form validation and error handling</li>
          <li>✅ Professional UI with glassmorphism effects</li>
          <li>✅ CRUD operations with MongoDB integration</li>
          <li>✅ Smart features notification system</li>
        </Box>
      </Box>

      <Box sx={styles.card}>
        <Typography variant="h6" sx={{ ...styles.primaryText, mb: 2 }}>
          📋 Test Scenarios
        </Typography>
        <Box component="ol" sx={{ color: '#ffffff', pl: 3 }}>
          <li><strong>Basic Animal:</strong> Add with just required fields (tag, species, breed, gender)</li>
          <li><strong>With Birth Date:</strong> Add animal with birth date to test lifecycle calculation</li>
          <li><strong>With Age:</strong> Add animal with age in months (alternative to birth date)</li>
          <li><strong>Full Lineage:</strong> Add animal with complete maternal and paternal lineage</li>
          <li><strong>Different Species:</strong> Test with cattle, sheep, goat, pig, chicken</li>
          <li><strong>Validation:</strong> Try submitting with missing required fields</li>
          <li><strong>Edge Cases:</strong> Test with very young/old animals for lifecycle stages</li>
        </Box>
      </Box>

      <AddAnimalForm
        open={showForm}
        onClose={() => setShowForm(false)}
        onSuccess={handleAddSuccess}
      />
    </Container>
  );
};

export default AddAnimalTest;
