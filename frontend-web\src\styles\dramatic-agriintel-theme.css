/**
 * DRAMATIC AGRIINTEL THEME - MAXIMUM VISIBILITY
 * Professional agricultural branding with enhanced visual impact
 */

/* Global AgriIntel Theme Variables */
:root {
  --agri-primary: #1565C0;
  --agri-secondary: #2E7D32;
  --agri-accent: #F57C00;
  --agri-primary-light: #42A5F5;
  --agri-secondary-light: #4CAF50;
  --agri-accent-light: #FFB74D;
  
  /* Dramatic Gradients */
  --agri-dramatic-gradient: linear-gradient(135deg,
    rgba(46, 125, 50, 0.98) 0%,
    rgba(21, 101, 192, 0.95) 15%,
    rgba(245, 124, 0, 0.90) 30%,
    rgba(46, 125, 50, 0.95) 45%,
    rgba(21, 101, 192, 0.98) 60%,
    rgba(245, 124, 0, 0.90) 75%,
    rgba(46, 125, 50, 0.98) 90%,
    rgba(21, 101, 192, 0.95) 100%);
    
  --agri-header-gradient: linear-gradient(90deg,
    var(--agri-primary) 0%,
    var(--agri-secondary) 50%,
    var(--agri-accent) 100%);
}

/* Enhanced Navigation Bar */
.landing-nav {
  background: var(--agri-header-gradient) !important;
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-bottom: 3px solid var(--agri-accent);
  box-shadow: 0 4px 20px rgba(21, 101, 192, 0.3);
  padding: 1rem 2rem;
  position: sticky;
  top: 0;
  z-index: 1000;
  animation: headerPulse 3s ease infinite;
}

.nav-brand h1 a {
  color: white !important;
  font-size: 2rem !important;
  font-weight: 800 !important;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  text-decoration: none !important;
}

.nav-links .nav-link {
  color: white !important;
  font-weight: 600 !important;
  font-size: 1.1rem !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  margin: 0 0.25rem;
}

.nav-links .nav-link:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Enhanced Buttons */
.btn-primary {
  background: var(--agri-accent) !important;
  border: 2px solid var(--agri-accent) !important;
  color: white !important;
  font-weight: 700 !important;
  padding: 0.75rem 1.5rem !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 15px rgba(245, 124, 0, 0.4) !important;
  transition: all 0.3s ease !important;
}

.btn-primary:hover {
  background: var(--agri-accent-light) !important;
  transform: translateY(-3px) !important;
  box-shadow: 0 8px 25px rgba(245, 124, 0, 0.6) !important;
}

.btn-secondary {
  background: transparent !important;
  border: 2px solid white !important;
  color: white !important;
  font-weight: 600 !important;
  padding: 0.75rem 1.5rem !important;
  border-radius: 12px !important;
  transition: all 0.3s ease !important;
}

.btn-secondary:hover {
  background: white !important;
  color: var(--agri-primary) !important;
  transform: translateY(-3px) !important;
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3) !important;
}

/* Dramatic Card Enhancements */
.MuiCard-root {
  background: linear-gradient(135deg,
    rgba(46, 125, 50, 0.1) 0%,
    rgba(21, 101, 192, 0.08) 50%,
    rgba(245, 124, 0, 0.05) 100%) !important;
  border: 2px solid rgba(46, 125, 50, 0.2) !important;
  box-shadow: 0 8px 32px rgba(21, 101, 192, 0.15) !important;
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
}

.MuiCard-root:hover {
  border-color: var(--agri-accent) !important;
  box-shadow: 0 12px 40px rgba(245, 124, 0, 0.25) !important;
  transform: translateY(-4px) scale(1.02) !important;
}

/* Enhanced Typography */
.MuiTypography-h1,
.MuiTypography-h2,
.MuiTypography-h3 {
  background: var(--agri-header-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800 !important;
}

/* Sidebar Enhancements */
.sidebar {
  background: var(--agri-dramatic-gradient) !important;
  border-right: 3px solid var(--agri-accent) !important;
  box-shadow: 4px 0 20px rgba(21, 101, 192, 0.2) !important;
}

/* Animation Keyframes */
@keyframes headerPulse {
  0%, 100% {
    box-shadow: 0 4px 20px rgba(21, 101, 192, 0.3);
  }
  50% {
    box-shadow: 0 6px 30px rgba(21, 101, 192, 0.5);
  }
}

@keyframes agriGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(46, 125, 50, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(21, 101, 192, 0.5);
  }
}

/* Apply glow to important elements */
.agri-enhanced {
  animation: agriGlow 2s ease infinite;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .landing-nav {
    padding: 0.75rem 1rem;
  }
  
  .nav-brand h1 a {
    font-size: 1.5rem !important;
  }
  
  .nav-links .nav-link {
    font-size: 1rem !important;
    padding: 0.4rem 0.8rem;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --agri-primary: #0D47A1;
    --agri-secondary: #1B5E20;
    --agri-accent: #E65100;
  }
  
  .landing-nav {
    border-bottom-width: 4px;
  }
  
  .MuiCard-root {
    border-width: 3px !important;
  }
}

/* Print Styles */
@media print {
  .landing-nav {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
  
  .nav-brand h1 a,
  .nav-links .nav-link {
    color: black !important;
    text-shadow: none !important;
  }
}
