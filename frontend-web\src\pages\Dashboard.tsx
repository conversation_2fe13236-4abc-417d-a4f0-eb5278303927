import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import {
  Box,
  useTheme,
  Grid,
  Card,
  CardContent,
  Typography,
  Tabs,
  Tab,
  Paper,
  IconButton,
  Chip,
  alpha,
  Button
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  ViewModule,
  Analytics,
  TrendingUp,
  Pets,
  LocalHospital,
  Science,
  Restaurant,
  AccountBalance,
  Inventory,
  Business,
  Assessment,
  School,
  Security,
  Settings,
  ArrowForward
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import ModuleSummary from '../components/ModuleSummary';
import MetricsChart from '../components/modules/reports/MetricsChart';
import SponsorCarousel from '../components/SponsorCarousel';
import ResourceLinks from '../components/ResourceLinks';
import UnifiedLayout from '../components/layout/UnifiedLayout';
import LivestockInsightsPanel from '../components/common/LivestockInsightsPanel';
import WeatherLivestockAdvisor from '../components/common/WeatherLivestockAdvisor';
import { realisticDataService } from '../services/realisticDataService';
import { ROUTES } from '../constants/routes';
import { withSubModuleTranslation, PageBackground } from '../components/common';
import AgriIntelBrand from '../components/branding/AgriIntelBrand';
import BetaKPIDashboard from '../components/beta/BetaKPIDashboard';

interface DashboardProps {
  translate?: (key: string, params?: Record<string, string | number>) => string;
  translateSubModule?: (field: string, fallback: string) => string;
  translateModuleField?: (field: string, fallback?: string) => string;
}

const Dashboard: React.FC<DashboardProps> = ({ translate, translateSubModule, translateModuleField }) => {
  const { user } = useAuth();
  const theme = useTheme();
  const navigate = useNavigate();
  const [animalData, setAnimalData] = useState<any[]>([]);
  const [healthData, setHealthData] = useState<any[]>([]);
  const [financialData, setFinancialData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState(0);

  // Check if user is BETA user
  const isBetaUser = user?.role === 'beta';

  // Load realistic data
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        const [animals, health, financial] = await Promise.all([
          realisticDataService.getAnimals(),
          realisticDataService.getHealthRecords(),
          realisticDataService.getFinancialData()
        ]);

        setAnimalData(animals);
        setHealthData(health);
        setFinancialData(financial);
      } catch (error) {
        console.error('Error loading dashboard data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Calculate dynamic stats from real data
  const dashboardStats = {
    total_animals: animalData.length,
    active_alerts: healthData.filter(h => h.type === 'Alert' || h.description?.includes('urgent')).length,
    pending_tasks: Math.floor(animalData.length * 0.1), // 10% need attention
    system_health: '98%',
    total_revenue: `R ${(financialData.filter(f => f.amount > 0).reduce((sum, f) => sum + f.amount, 0) / 1000).toFixed(0)}K`,
    feed_stock: '85%'
  };

  // Management Modules with enhanced data
  const managementModules = [
    {
      name: translate('nav.animals'),
      path: ROUTES.ANIMALS,
      icon: Pets,
      count: animalData.length,
      status: translate('dashboard.animals_attention', { count: animalData.filter(a => a.healthStatus !== 'Healthy').length }),
      color: theme.palette.primary.main,
      description: 'Manage livestock inventory and tracking',
      trend: '+12%'
    },
    {
      name: translate('nav.health'),
      path: ROUTES.HEALTH,
      icon: LocalHospital,
      count: healthData.length,
      status: translate('dashboard.pending_checkups', { count: healthData.filter(h => h.type === 'Checkup').length }),
      color: '#10b981',
      description: 'Health monitoring and veterinary care',
      trend: '+8%'
    },
    {
      name: translate('nav.breeding'),
      path: ROUTES.BREEDING,
      icon: Science,
      count: animalData.filter(a => a.status === 'Breeding' || a.healthStatus === 'Pregnant').length,
      status: translate('dashboard.due_dates', { count: 8 }),
      color: '#8b5cf6',
      description: 'Breeding programs and genetics',
      trend: '+15%'
    },
    {
      name: translate('nav.feeding'),
      path: ROUTES.FEED,
      icon: Restaurant,
      count: 8,
      status: translate('dashboard.low_stock', { count: 2 }),
      color: '#f59e0b',
      description: 'Feed management and nutrition',
      trend: '-3%'
    },
    {
      name: translate('nav.financial'),
      path: ROUTES.FINANCIAL,
      icon: AccountBalance,
      count: dashboardStats.total_revenue,
      status: translate('dashboard.revenue_month'),
      color: '#059669',
      description: 'Financial tracking and budgeting',
      trend: '+22%'
    },
    {
      name: translate('nav.inventory'),
      path: ROUTES.INVENTORY,
      icon: Inventory,
      count: 156,
      status: '12 items low stock',
      color: '#3b82f6',
      description: 'Inventory and supply management',
      trend: '+5%'
    },
    {
      name: translate('nav.commercial'),
      path: ROUTES.COMMERCIAL,
      icon: Business,
      count: 24,
      status: '8 active contracts',
      color: '#dc2626',
      description: 'Sales and commercial activities',
      trend: '+18%'
    },
    {
      name: translate('nav.reports'),
      path: ROUTES.REPORTS,
      icon: Assessment,
      count: 45,
      status: '12 new reports',
      color: '#0891b2',
      description: 'Reports and documentation',
      trend: '+7%'
    },
    {
      name: translate('nav.resources'),
      path: ROUTES.RESOURCES,
      icon: School,
      count: 89,
      status: '15 new resources',
      color: '#059669',
      description: 'Educational resources and guides',
      trend: '+10%'
    },
    {
      name: translate('nav.compliance'),
      path: ROUTES.COMPLIANCE,
      icon: Security,
      count: 12,
      status: '3 pending audits',
      color: '#dc2626',
      description: 'Regulatory compliance tracking',
      trend: '+2%'
    },
    {
      name: translate('nav.settings'),
      path: ROUTES.SETTINGS,
      icon: Settings,
      count: 8,
      status: 'System configured',
      color: '#6b7280',
      description: 'System settings and preferences',
      trend: '0%'
    },
    {
      name: translate('dashboard.analytics'),
      path: ROUTES.ANALYTICS,
      icon: Analytics,
      count: 15,
      status: translate('dashboard.new_insights', { count: 5 }),
      color: '#7c3aed',
      description: 'Advanced analytics and insights',
      trend: '+25%'
    }
  ];

  // Tab change handler
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Module card click handler
  const handleModuleClick = (path: string) => {
    navigate(path);
  };

  // Render module card component
  const ModuleCard = ({ module, index }: { module: any, index: number }) => {
    const IconComponent = module.icon;
    const isPositiveTrend = module.trend.startsWith('+');

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: index * 0.1 }}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <Card
          sx={{
            height: '100%',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            background: `
              linear-gradient(
                135deg,
                ${alpha(module.color, 0.15)} 0%,
                ${alpha(module.color, 0.08)} 50%,
                ${alpha(module.color, 0.12)} 100%
              )
            `,
            border: `2px solid ${alpha(module.color, 0.25)}`,
            borderRadius: 3,
            backdropFilter: 'blur(10px)',
            boxShadow: `0 8px 32px ${alpha(module.color, 0.15)}`,
            '&:hover': {
              transform: 'translateY(-8px) scale(1.02)',
              boxShadow: `0 20px 40px ${alpha(module.color, 0.3)}`,
              border: `2px solid ${alpha(module.color, 0.5)}`,
              background: `
                linear-gradient(
                  135deg,
                  ${alpha(module.color, 0.25)} 0%,
                  ${alpha(module.color, 0.15)} 50%,
                  ${alpha(module.color, 0.2)} 100%
                )
              `,
            }
          }}
          onClick={() => handleModuleClick(module.path)}
        >
          <CardContent sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 2 }}>
              <Box
                sx={{
                  p: 1.5,
                  borderRadius: 2,
                  backgroundColor: alpha(module.color, 0.1),
                  border: `1px solid ${alpha(module.color, 0.2)}`
                }}
              >
                <IconComponent sx={{ fontSize: 32, color: module.color }} />
              </Box>
              <Chip
                label={module.trend}
                size="small"
                sx={{
                  backgroundColor: isPositiveTrend ? alpha('#10b981', 0.1) : alpha('#ef4444', 0.1),
                  color: isPositiveTrend ? '#10b981' : '#ef4444',
                  fontWeight: 'bold',
                  fontSize: '0.75rem'
                }}
              />
            </Box>

            <Typography variant="h4" fontWeight="bold" sx={{ mb: 1, fontSize: '2rem' }}>
              {module.count}
            </Typography>

            <Typography variant="h6" fontWeight="600" sx={{ mb: 1, fontSize: '1.25rem' }}>
              {module.name}
            </Typography>

            <Typography variant="body2" color="text.secondary" sx={{ mb: 2, fontSize: '1rem' }}>
              {module.description}
            </Typography>

            <Typography variant="body2" color="text.primary" sx={{ fontWeight: 500, fontSize: '0.95rem' }}>
              {module.status}
            </Typography>

            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
              <IconButton
                size="small"
                sx={{
                  backgroundColor: alpha(module.color, 0.1),
                  color: module.color,
                  '&:hover': {
                    backgroundColor: alpha(module.color, 0.2),
                  }
                }}
              >
                <ArrowForward fontSize="small" />
              </IconButton>
            </Box>
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  return (
    <UnifiedLayout
      title="AgriIntel Dashboard"
      subtitle="Your comprehensive solution for intelligent livestock management"
      backgroundImage="/images/modules/animals/cattle-1.jpeg"
      backgroundPosition="right"
      showBackground={true}
      maxWidth={false}
    >
      {/* Header Section */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <Paper
          elevation={0}
          sx={{
            p: 4,
            mb: 3,
            background: `
              linear-gradient(
                135deg,
                rgba(46, 125, 50, 0.98) 0%,
                rgba(21, 101, 192, 0.95) 15%,
                rgba(245, 124, 0, 0.90) 30%,
                rgba(46, 125, 50, 0.95) 45%,
                rgba(21, 101, 192, 0.98) 60%,
                rgba(245, 124, 0, 0.90) 75%,
                rgba(46, 125, 50, 0.98) 90%,
                rgba(21, 101, 192, 0.95) 100%
              ),
              url('/images/modules/animals/cattle-2.avif')
            `,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundBlendMode: 'overlay',
            color: 'white',
            borderRadius: 3,
            position: 'relative',
            overflow: 'hidden',
            backdropFilter: 'blur(5px)',
            boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)'
          }}
        >
          <Box sx={{ position: 'relative', zIndex: 2 }}>
            <AgriIntelBrand
              variant="full"
              size="large"
              showSlogan={true}
              color="white"
              orientation="horizontal"
              sx={{ mb: 2 }}
            />
            <Typography variant="h6" sx={{ opacity: 0.9, fontSize: { xs: '1.1rem', md: '1.25rem' } }}>
              Your comprehensive solution for intelligent livestock management
            </Typography>
          </Box>

          {/* Decorative elements */}
          <Box
            sx={{
              position: 'absolute',
              top: -50,
              right: -50,
              width: 200,
              height: 200,
              borderRadius: '50%',
              background: alpha('#ffffff', 0.1),
              zIndex: 1
            }}
          />
        </Paper>
      </motion.div>

      {/* Main Dashboard Tabs */}
      <Paper elevation={0} sx={{ mb: 3, borderRadius: 3, overflow: 'hidden' }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="fullWidth"
          sx={{
            backgroundColor: alpha(theme.palette.primary.main, 0.05),
            '& .MuiTab-root': {
              fontSize: '1.1rem',
              fontWeight: 600,
              py: 2,
              minHeight: 64,
              textTransform: 'none',
              '&.Mui-selected': {
                backgroundColor: theme.palette.primary.main,
                color: 'white',
              }
            },
            '& .MuiTabs-indicator': {
              display: 'none'
            }
          }}
        >
          <Tab
            icon={<DashboardIcon sx={{ fontSize: 24 }} />}
            iconPosition="start"
            label="Overview"
            sx={{ fontSize: '1.1rem' }}
          />
          <Tab
            icon={<ViewModule sx={{ fontSize: 24 }} />}
            iconPosition="start"
            label="Management Modules"
            sx={{ fontSize: '1.1rem' }}
          />
          <Tab
            icon={<Analytics sx={{ fontSize: 24 }} />}
            iconPosition="start"
            label="Analytics"
            sx={{ fontSize: '1.1rem' }}
          />
          {isBetaUser && (
            <Tab
              icon={<TrendingUp sx={{ fontSize: 24 }} />}
              iconPosition="start"
              label="KPI Dashboard (BETA)"
              sx={{ fontSize: '1.1rem' }}
            />
          )}
        </Tabs>
      </Paper>

      {/* Tab Content */}
      <AnimatePresence mode="wait">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
        >
          {/* Overview Tab */}
          {activeTab === 0 && (
            <Grid container spacing={3}>
              {/* Key Metrics Cards */}
              <Grid item xs={12} md={6} lg={3}>
                <Card sx={{
                  p: 3,
                  textAlign: 'center',
                  borderRadius: 3,
                  background: 'linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.05))',
                  border: '2px solid rgba(34, 197, 94, 0.2)',
                  backdropFilter: 'blur(10px)',
                  boxShadow: '0 8px 32px rgba(34, 197, 94, 0.15)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 12px 40px rgba(34, 197, 94, 0.25)'
                  }
                }}>
                  <Typography variant="h3" color="primary" fontWeight="bold" sx={{ fontSize: { xs: '2rem', md: '2.5rem' } }}>
                    {dashboardStats.total_animals}
                  </Typography>
                  <Typography variant="h6" sx={{ fontSize: { xs: '1rem', md: '1.2rem' } }}>Total Animals</Typography>
                </Card>
              </Grid>
              <Grid item xs={12} md={6} lg={3}>
                <Card sx={{
                  p: 3,
                  textAlign: 'center',
                  borderRadius: 3,
                  background: 'linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05))',
                  border: '2px solid rgba(239, 68, 68, 0.2)',
                  backdropFilter: 'blur(10px)',
                  boxShadow: '0 8px 32px rgba(239, 68, 68, 0.15)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 12px 40px rgba(239, 68, 68, 0.25)'
                  }
                }}>
                  <Typography variant="h3" color="error" fontWeight="bold" sx={{ fontSize: { xs: '2rem', md: '2.5rem' } }}>
                    {dashboardStats.active_alerts}
                  </Typography>
                  <Typography variant="h6" sx={{ fontSize: { xs: '1rem', md: '1.2rem' } }}>Active Alerts</Typography>
                </Card>
              </Grid>
              <Grid item xs={12} md={6} lg={3}>
                <Card sx={{
                  p: 3,
                  textAlign: 'center',
                  borderRadius: 3,
                  background: 'linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05))',
                  border: '2px solid rgba(16, 185, 129, 0.2)',
                  backdropFilter: 'blur(10px)',
                  boxShadow: '0 8px 32px rgba(16, 185, 129, 0.15)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 12px 40px rgba(16, 185, 129, 0.25)'
                  }
                }}>
                  <Typography variant="h3" color="success.main" fontWeight="bold" sx={{ fontSize: { xs: '2rem', md: '2.5rem' } }}>
                    {dashboardStats.total_revenue}
                  </Typography>
                  <Typography variant="h6" sx={{ fontSize: { xs: '1rem', md: '1.2rem' } }}>Monthly Revenue</Typography>
                </Card>
              </Grid>
              <Grid item xs={12} md={6} lg={3}>
                <Card sx={{
                  p: 3,
                  textAlign: 'center',
                  borderRadius: 3,
                  background: 'linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(245, 158, 11, 0.05))',
                  border: '2px solid rgba(245, 158, 11, 0.2)',
                  backdropFilter: 'blur(10px)',
                  boxShadow: '0 8px 32px rgba(245, 158, 11, 0.15)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 12px 40px rgba(245, 158, 11, 0.25)'
                  }
                }}>
                  <Typography variant="h3" color="warning.main" fontWeight="bold" sx={{ fontSize: { xs: '2rem', md: '2.5rem' } }}>
                    {dashboardStats.feed_stock}
                  </Typography>
                  <Typography variant="h6" sx={{ fontSize: { xs: '1rem', md: '1.2rem' } }}>Feed Stock Level</Typography>
                </Card>
              </Grid>

              {/* AI Insights and Weather */}
              <Grid item xs={12} lg={6}>
                <LivestockInsightsPanel
                  module="dashboard"
                  animalData={animalData}
                  compact={false}
                />
              </Grid>
              <Grid item xs={12} lg={6}>
                <WeatherLivestockAdvisor
                  animalTypes={['cattle', 'sheep', 'goats']}
                  compact={false}
                />
              </Grid>
            </Grid>
          )}

          {/* Management Modules Tab */}
          {activeTab === 1 && (
            <Box sx={{ width: '100%' }}>
              <Typography variant="h4" fontWeight="bold" sx={{
                mb: 3,
                fontSize: { xs: '1.5rem', md: '1.8rem', lg: '2rem' },
                textAlign: 'center',
                color: 'primary.main'
              }}>
                Management Modules
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{
                mb: 4,
                fontSize: { xs: '1rem', md: '1.1rem', lg: '1.2rem' },
                textAlign: 'center',
                maxWidth: '800px',
                mx: 'auto',
                lineHeight: 1.6
              }}>
                Access all your livestock management tools and modules from here. Click on any module to navigate directly to its dashboard.
              </Typography>

              <Grid container spacing={{ xs: 2, md: 3, lg: 4 }} sx={{
                justifyContent: 'center',
                width: '100%',
                margin: 0
              }}>
                {managementModules.map((module, index) => (
                  <Grid item xs={12} sm={6} md={4} lg={3} xl={2.4} key={module.name} sx={{
                    display: 'flex',
                    justifyContent: 'center'
                  }}>
                    <Box sx={{ width: '100%', maxWidth: '350px' }}>
                      <ModuleCard module={module} index={index} />
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}

          {/* Analytics Tab */}
          {activeTab === 2 && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="h4" fontWeight="bold" sx={{ mb: 3, fontSize: '1.8rem' }}>
                  Analytics & Insights
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <MetricsChart
                  title={translate('dashboard.health_trends')}
                  description={translate('dashboard.weekly_health')}
                  type="line"
                  apiEndpoint="metrics/livestock"
                  timeRange="week"
                  refreshInterval={300000}
                  initialData={{
                    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                    datasets: [
                      {
                        label: 'Health Checks',
                        data: [12, 19, 15, 17, 22, 24, 20],
                        color: '#4FBEB4'
                      },
                      {
                        label: 'Treatments',
                        data: [5, 8, 6, 9, 12, 10, 7],
                        color: '#38B2AC'
                      }
                    ]
                  }}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <MetricsChart
                  title={translate('dashboard.financial_performance')}
                  description={translate('dashboard.monthly_revenue')}
                  type="bar"
                  apiEndpoint="metrics/financial"
                  timeRange="month"
                  initialData={{
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [
                      {
                        label: 'Revenue',
                        data: [45000, 52000, 49000, 60000, 55000, 65000],
                        color: '#4FBEB4'
                      },
                      {
                        label: 'Expenses',
                        data: [30000, 35000, 32000, 38000, 36000, 40000],
                        color: '#F87171'
                      }
                    ]
                  }}
                />
              </Grid>

              <Grid item xs={12}>
                <Card sx={{ p: 3, borderRadius: 3 }}>
                  <Typography variant="h6" fontWeight="bold" sx={{ mb: 2, fontSize: '1.3rem' }}>
                    Performance Summary
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6} md={3}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" color="primary" fontWeight="bold">98%</Typography>
                        <Typography variant="body2" sx={{ fontSize: '1rem' }}>System Health</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6} md={3}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" color="success.main" fontWeight="bold">+15%</Typography>
                        <Typography variant="body2" sx={{ fontSize: '1rem' }}>Growth Rate</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6} md={3}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" color="warning.main" fontWeight="bold">85%</Typography>
                        <Typography variant="body2" sx={{ fontSize: '1rem' }}>Efficiency</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6} md={3}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" color="info.main" fontWeight="bold">24/7</Typography>
                        <Typography variant="body2" sx={{ fontSize: '1rem' }}>Monitoring</Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </Card>
              </Grid>
            </Grid>
          )}

          {/* KPI Dashboard Tab (BETA only) */}
          {isBetaUser && activeTab === 3 && (
            <BetaKPIDashboard />
          )}
        </motion.div>
      </AnimatePresence>

      {/* Sponsor Carousel - Bottom */}
      <Box sx={{ mt: 6 }}>
        <SponsorCarousel title={translate('dashboard.sponsors')} interval={10000} />
      </Box>

      {/* Resource Links for Farmers */}
      <Box sx={{ mt: 4 }}>
        <ResourceLinks />
      </Box>
    </UnifiedLayout>
  );
};

// Wrap the component with our HOC to provide translation functions
export default withSubModuleTranslation(Dashboard, 'dashboard', 'main');
