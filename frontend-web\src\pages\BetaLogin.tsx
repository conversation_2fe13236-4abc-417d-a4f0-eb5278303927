import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import {
  <PERSON><PERSON><PERSON>,
  TextField,
  Button,
  Alert,
  InputAdornment,
  IconButton,
  CircularProgress,
  Box,
  useTheme,
  alpha,
  Chip,
  Divider
} from '@mui/material';
import {
  Person,
  Lock,
  Visibility,
  VisibilityOff,
  Agriculture
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import LanguageSelector from '../components/common/LanguageSelector';
import UnifiedLayout from '../components/layout/UnifiedLayout';

const BetaLogin: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const { login, isLoading, error } = useAuth();
  const { translate } = useLanguage();
  const navigate = useNavigate();
  const location = useLocation();

  // Get the intended destination or default to dashboard
  const from = location.state?.from || '/dashboard';

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!username.trim() || !password.trim()) return;

    try {
      await login(username, password);
      // Navigate to beta dashboard (beta users see limited modules)
      navigate('/beta-dashboard', { replace: true });
    } catch (err) {
      console.error('Login failed:', err);
    }
  };

  const theme = useTheme();

  return (
    <UnifiedLayout
      title="Welcome to AgriIntel BETA"
      subtitle="Smart Farming, Smarter Decisions - Beta Access"
      backgroundImage="/images/animals/cattle-1.jpeg"
      backgroundPosition="left"
      showBackground={true}
    >
      {/* Language Selector */}
      <Box display="flex" justifyContent="flex-end" mb={2}>
        <LanguageSelector variant="compact" showLabel={false} size="small" />
      </Box>

      <Box textAlign="center" mb={4}>
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.5, type: "spring", stiffness: 200 }}
        >
          <Box
            sx={{
              width: 80,
              height: 80,
              borderRadius: '50%',
              background: 'linear-gradient(135deg, #42AF9B, #66BB6A)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 16px',
              boxShadow: '0 8px 32px rgba(66, 175, 155, 0.3)',
            }}
          >
            <Agriculture sx={{ fontSize: 40, color: 'white' }} />
          </Box>
        </motion.div>

        {/* BETA Label */}
        <Chip 
          label="BETA ACCESS" 
          color="secondary" 
          size="small" 
          sx={{ 
            mb: 2,
            fontWeight: 'bold',
            background: 'linear-gradient(45deg, #FF6B35, #F7931E)',
            color: 'white'
          }} 
        />

        <Typography
          variant="h4"
          sx={{
            fontWeight: 700,
            color: theme.palette.text.primary,
            mb: 1,
          }}
        >
          {translate('login.title', { fallback: 'Sign In to BETA' })}
        </Typography>

        <Typography
          variant="body2"
          sx={{
            color: theme.palette.text.secondary,
            mb: 2,
          }}
        >
          Access your beta features and limited modules
        </Typography>

        {/* Test Credentials */}
        <Box display="flex" gap={1} justifyContent="center" flexWrap="wrap" mb={2}>
          <Chip
            label="Admin: admin/Admin@123"
            size="small"
            variant="outlined"
            onClick={() => { setUsername('admin'); setPassword('Admin@123'); }}
            sx={{ cursor: 'pointer', '&:hover': { backgroundColor: alpha(theme.palette.primary.main, 0.1) } }}
          />
          <Chip
            label="May: May Rakgama/MayAdmin@2024"
            size="small"
            variant="outlined"
            onClick={() => { setUsername('May Rakgama'); setPassword('MayAdmin@2024'); }}
            sx={{ cursor: 'pointer', '&:hover': { backgroundColor: alpha(theme.palette.primary.main, 0.1) } }}
          />
        </Box>
      </Box>

      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Alert
              severity="error"
              sx={{
                mb: 3,
                borderRadius: 2,
                '& .MuiAlert-icon': {
                  fontSize: 20,
                }
              }}
            >
              {error}
            </Alert>
          </motion.div>
        )}
      </AnimatePresence>

      <form onSubmit={handleLogin}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.5 }}
        >
          <TextField
            label={translate('login.username', { fallback: 'Username' })}
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            fullWidth
            margin="normal"
            variant="outlined"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Person sx={{ color: theme.palette.primary.main }} />
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: 2,
                transition: 'all 0.3s ease',
                '&:hover': {
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme.palette.primary.main,
                    borderWidth: 2,
                  },
                },
                '&.Mui-focused': {
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme.palette.primary.main,
                    borderWidth: 2,
                    boxShadow: `0 0 0 3px ${alpha(theme.palette.primary.main, 0.1)}`,
                  },
                },
              },
            }}
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7, duration: 0.5 }}
        >
          <TextField
            label={translate('login.password', { fallback: 'Password' })}
            type={showPassword ? 'text' : 'password'}
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            fullWidth
            margin="normal"
            variant="outlined"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Lock sx={{ color: theme.palette.primary.main }} />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => setShowPassword(!showPassword)}
                    edge="end"
                    sx={{
                      color: theme.palette.text.secondary,
                      '&:hover': {
                        color: theme.palette.primary.main,
                      }
                    }}
                  >
                    {showPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: 2,
                transition: 'all 0.3s ease',
                '&:hover': {
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme.palette.primary.main,
                    borderWidth: 2,
                  },
                },
                '&.Mui-focused': {
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme.palette.primary.main,
                    borderWidth: 2,
                    boxShadow: `0 0 0 3px ${alpha(theme.palette.primary.main, 0.1)}`,
                  },
                },
              },
            }}
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.5 }}
        >
          <Button
            type="submit"
            variant="contained"
            fullWidth
            disabled={isLoading}
            sx={{
              mt: 3,
              mb: 2,
              height: 56,
              borderRadius: 2,
              fontSize: '1rem',
              fontWeight: 600,
              background: 'linear-gradient(135deg, #42AF9B 0%, #66BB6A 100%)',
              boxShadow: '0 8px 32px rgba(66, 175, 155, 0.3)',
              transition: 'all 0.3s ease',
              '&:hover': {
                background: 'linear-gradient(135deg, #3CA893 0%, #5CB85C 100%)',
                transform: 'translateY(-2px)',
                boxShadow: '0 12px 40px rgba(66, 175, 155, 0.4)',
              },
              '&:disabled': {
                background: 'linear-gradient(135deg, #9E9E9E 0%, #BDBDBD 100%)',
              },
            }}
          >
            {isLoading ? (
              <CircularProgress size={24} sx={{ color: 'white' }} />
            ) : (
              'SIGN IN TO BETA'
            )}
          </Button>
        </motion.div>
      </form>

      <Divider sx={{ my: 3 }}>
        <Typography variant="caption" sx={{ color: theme.palette.text.secondary }}>
          Beta Access Login
        </Typography>
      </Divider>

      <Box textAlign="center">
        <Typography variant="caption" sx={{ color: theme.palette.text.secondary }}>
          Beta version with limited features - Upgrade for full access
        </Typography>
      </Box>
    </UnifiedLayout>
  );
};

export default BetaLogin;
