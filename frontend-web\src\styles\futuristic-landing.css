/*
 * AGRIINTEL 2050 FUTURISTIC LANDING PAGE
 * Next-generation design with holographic effects, neural networks, and AI aesthetics
 */

:root {
  /* 2050 Color Palette */
  --neo-primary: #00D4FF;
  --neo-secondary: #7C3AED;
  --neo-accent: #F59E0B;
  --neo-success: #10B981;
  --neo-warning: #F59E0B;
  --neo-error: #EF4444;
  
  /* Holographic Colors */
  --holo-cyan: #00FFFF;
  --holo-magenta: #FF00FF;
  --holo-yellow: #FFFF00;
  --holo-green: #00FF00;
  
  /* Neural Network Colors */
  --neural-blue: #1E40AF;
  --neural-purple: #7C3AED;
  --neural-pink: #EC4899;
  --neural-teal: #0D9488;
  
  /* Glass Morphism */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  
  /* Neon <PERSON>lows */
  --neon-glow-blue: 0 0 20px #00D4FF, 0 0 40px #00D4FF, 0 0 60px #00D4FF;
  --neon-glow-purple: 0 0 20px #7C3AED, 0 0 40px #7C3AED, 0 0 60px #7C3AED;
  --neon-glow-green: 0 0 20px #10B981, 0 0 40px #10B981, 0 0 60px #10B981;
}

/* Futuristic Background - TRANSPARENT FOR TAB BACKGROUNDS */
.agri-landing-main {
  position: relative;
  min-height: 100vh;
  background: transparent; /* Remove solid background to show tab images */
  overflow-x: hidden;
}

.agri-landing-main::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(124, 58, 237, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
  z-index: 0;
  animation: backgroundPulse 8s ease-in-out infinite;
}

@keyframes backgroundPulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

/* Animated Neural Network Background */
.neural-network-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.neural-node {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--neo-primary);
  border-radius: 50%;
  box-shadow: var(--neon-glow-blue);
  animation: neuralPulse 3s ease-in-out infinite;
}

.neural-connection {
  position: absolute;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--neo-primary), transparent);
  animation: dataFlow 2s linear infinite;
}

@keyframes neuralPulse {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.5); }
}

@keyframes dataFlow {
  0% { opacity: 0; transform: translateX(-100%); }
  50% { opacity: 1; }
  100% { opacity: 0; transform: translateX(100%); }
}

/* Futuristic Navigation */
.agri-nav {
  position: relative;
  z-index: 100;
  background: rgba(15, 23, 42, 0.8) !important;
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 212, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.agri-nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
}

.agri-logo {
  filter: drop-shadow(0 0 10px var(--neo-primary));
  transition: all 0.3s ease;
}

.agri-logo:hover {
  filter: drop-shadow(0 0 20px var(--neo-primary));
  transform: scale(1.05);
}

.agri-brand-title {
  background: linear-gradient(135deg, var(--neo-primary), var(--neo-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800 !important;
  text-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
}

.agri-brand-subtitle {
  color: rgba(255, 255, 255, 0.7) !important;
  font-weight: 300 !important;
}

.agri-status-badge {
  background: linear-gradient(135deg, var(--neo-success), var(--holo-green)) !important;
  color: white !important;
  font-weight: 600 !important;
  box-shadow: var(--neon-glow-green);
  animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
  0%, 100% { box-shadow: var(--neon-glow-green); }
  50% { box-shadow: 0 0 30px #10B981, 0 0 60px #10B981, 0 0 90px #10B981; }
}

/* Hero Section - Normal Scale */
.agri-hero-section {
  position: relative;
  z-index: 10;
  padding: 4rem 0 3rem;
  text-align: center;
}

.agri-hero-content {
  position: relative;
  z-index: 2;
}

.agri-hero-title {
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  line-height: 1.2 !important;
  margin-bottom: 1.5rem !important;
  background: linear-gradient(135deg, #FFFFFF 0%, var(--neo-primary) 50%, var(--neo-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
}

.agri-hero-accent {
  display: block;
  background: linear-gradient(135deg, var(--neo-accent), var(--holo-yellow));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: accentShimmer 2s ease-in-out infinite;
}

@keyframes titleGlow {
  0%, 100% { text-shadow: 0 0 50px rgba(0, 212, 255, 0.3); }
  50% { text-shadow: 0 0 80px rgba(0, 212, 255, 0.6), 0 0 120px rgba(124, 58, 237, 0.4); }
}

@keyframes accentShimmer {
  0%, 100% { filter: hue-rotate(0deg); }
  50% { filter: hue-rotate(30deg); }
}

.agri-hero-subtitle {
  font-size: 1.2rem !important;
  color: rgba(255, 255, 255, 0.9) !important;
  margin-bottom: 2rem !important;
  font-weight: 400 !important;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* CTA Buttons - Normal Scale */
.agri-hero-actions {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 3rem;
}

.agri-cta-primary {
  background: linear-gradient(135deg, var(--neo-primary), var(--neural-blue)) !important;
  color: white !important;
  font-weight: 700 !important;
  font-size: 1.1rem !important;
  padding: 1rem 3rem !important;
  border-radius: 50px !important;
  border: 2px solid transparent !important;
  box-shadow: var(--neon-glow-blue), 0 10px 30px rgba(0, 0, 0, 0.3) !important;
  transition: all 0.3s ease !important;
  position: relative;
  overflow: hidden;
}

.agri-cta-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.agri-cta-primary:hover::before {
  left: 100%;
}

.agri-cta-primary:hover {
  transform: translateY(-3px) scale(1.05) !important;
  box-shadow: 0 0 40px #00D4FF, 0 0 80px #00D4FF, 0 15px 40px rgba(0, 0, 0, 0.4) !important;
}

.agri-cta-secondary {
  background: rgba(255, 255, 255, 0.1) !important;
  color: white !important;
  font-weight: 600 !important;
  font-size: 1.1rem !important;
  padding: 1rem 3rem !important;
  border-radius: 50px !important;
  border: 2px solid var(--neo-secondary) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  backdrop-filter: blur(10px) !important;
  box-shadow: var(--neon-glow-purple), 0 10px 30px rgba(0, 0, 0, 0.2) !important;
  transition: all 0.3s ease !important;
}

.agri-cta-secondary:hover {
  background: rgba(124, 58, 237, 0.2) !important;
  transform: translateY(-3px) scale(1.05) !important;
  box-shadow: 0 0 40px #7C3AED, 0 0 80px #7C3AED, 0 15px 40px rgba(0, 0, 0, 0.4) !important;
}

/* Holographic Feature Cards */
.agri-features-section {
  position: relative;
  z-index: 10;
  padding: 6rem 0;
}

.agri-feature-card {
  background: var(--glass-bg) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid var(--glass-border) !important;
  border-radius: 20px !important;
  box-shadow: var(--glass-shadow) !important;
  transition: all 0.3s ease !important;
  position: relative;
  overflow: hidden;
}

.agri-feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--neo-primary), var(--neo-secondary), var(--neo-accent));
  animation: borderFlow 3s linear infinite;
}

@keyframes borderFlow {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.agri-feature-card:hover {
  transform: translateY(-10px) !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3), var(--neon-glow-blue) !important;
  border-color: var(--neo-primary) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .agri-hero-section {
    padding: 2rem 0 1.5rem;
  }

  .agri-hero-title {
    font-size: 1.8rem !important;
  }

  .agri-hero-subtitle {
    font-size: 1rem !important;
    margin-bottom: 1.5rem !important;
  }

  .agri-hero-actions {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .agri-cta-primary,
  .agri-cta-secondary {
    width: 100%;
    max-width: 280px;
    padding: 0.875rem 2rem !important;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --neo-primary: #00BFFF;
    --neo-secondary: #9333EA;
    --glass-bg: rgba(255, 255, 255, 0.2);
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .agri-landing-main::before,
  .neural-node,
  .neural-connection,
  .agri-status-badge,
  .agri-hero-title,
  .agri-hero-accent {
    animation: none !important;
  }
}
