/**
 * BETA Functionality Test Suite
 * Comprehensive testing for all BETA tier features and fixes
 */

export interface TestResult {
  testName: string;
  passed: boolean;
  message: string;
  details?: any;
}

export interface BetaTestSuite {
  dataPopulation: TestResult[];
  formFunctionality: TestResult[];
  loginImages: TestResult[];
  themeIntegration: TestResult[];
  overall: {
    totalTests: number;
    passedTests: number;
    failedTests: number;
    successRate: number;
  };
}

/**
 * Test data population in BETA modules
 */
export const testDataPopulation = async (): Promise<TestResult[]> => {
  const results: TestResult[] = [];

  // Test Animals data
  try {
    const animalsResponse = await fetch('http://localhost:3002/api/animals');
    const animalsData = await animalsResponse.json();
    
    results.push({
      testName: 'Animals API Response',
      passed: animalsResponse.ok,
      message: animalsResponse.ok ? 'Animals API responding correctly' : 'Animals API failed',
      details: { status: animalsResponse.status, dataCount: animalsData?.animals?.length || 0 }
    });

    // Check for BETA sample data
    const hasBetaAnimals = animalsData?.animals?.some((animal: any) => 
      animal.tagNumber?.startsWith('BETA-') || animal.name === 'Tshepiso' || animal.name === 'Lerato'
    );
    
    results.push({
      testName: 'BETA Sample Animals',
      passed: hasBetaAnimals,
      message: hasBetaAnimals ? 'BETA sample animals found' : 'BETA sample animals missing',
      details: { betaAnimalsFound: hasBetaAnimals }
    });

  } catch (error) {
    results.push({
      testName: 'Animals API Connection',
      passed: false,
      message: 'Failed to connect to Animals API',
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    });
  }

  // Test Health records
  try {
    const healthResponse = await fetch('http://localhost:3002/api/health/records');
    const healthData = await healthResponse.json();
    
    results.push({
      testName: 'Health API Response',
      passed: healthResponse.ok,
      message: healthResponse.ok ? 'Health API responding correctly' : 'Health API failed',
      details: { status: healthResponse.status, dataCount: healthData?.records?.length || 0 }
    });

  } catch (error) {
    results.push({
      testName: 'Health API Connection',
      passed: false,
      message: 'Failed to connect to Health API',
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    });
  }

  // Test Feeding records
  try {
    const feedingResponse = await fetch('http://localhost:3002/api/feeding/records');
    const feedingData = await feedingResponse.json();
    
    results.push({
      testName: 'Feeding API Response',
      passed: feedingResponse.ok,
      message: feedingResponse.ok ? 'Feeding API responding correctly' : 'Feeding API failed',
      details: { status: feedingResponse.status, dataCount: feedingData?.records?.length || 0 }
    });

  } catch (error) {
    results.push({
      testName: 'Feeding API Connection',
      passed: false,
      message: 'Failed to connect to Feeding API',
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    });
  }

  // Test Financial records
  try {
    const financialResponse = await fetch('http://localhost:3002/api/financial/records');
    const financialData = await financialResponse.json();
    
    results.push({
      testName: 'Financial API Response',
      passed: financialResponse.ok,
      message: financialResponse.ok ? 'Financial API responding correctly' : 'Financial API failed',
      details: { status: financialResponse.status, dataCount: financialData?.records?.length || 0 }
    });

  } catch (error) {
    results.push({
      testName: 'Financial API Connection',
      passed: false,
      message: 'Failed to connect to Financial API',
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    });
  }

  return results;
};

/**
 * Test form functionality
 */
export const testFormFunctionality = (): TestResult[] => {
  const results: TestResult[] = [];

  // Test if buttons are clickable
  const addAnimalButton = document.querySelector('[data-testid="add-animal-button"]') || 
                          document.querySelector('button:contains("Add Animal")');
  
  results.push({
    testName: 'Add Animal Button',
    passed: !!addAnimalButton,
    message: addAnimalButton ? 'Add Animal button found' : 'Add Animal button not found',
    details: { buttonExists: !!addAnimalButton }
  });

  // Test form fields
  const nameField = document.querySelector('input[name="name"]') || 
                   document.querySelector('input[placeholder*="name" i]');
  
  results.push({
    testName: 'Animal Name Field',
    passed: !!nameField,
    message: nameField ? 'Animal name field found' : 'Animal name field not found',
    details: { fieldExists: !!nameField }
  });

  // Test species dropdown
  const speciesSelect = document.querySelector('select[name="species"]') || 
                       document.querySelector('[role="combobox"]');
  
  results.push({
    testName: 'Species Selection',
    passed: !!speciesSelect,
    message: speciesSelect ? 'Species selection found' : 'Species selection not found',
    details: { selectExists: !!speciesSelect }
  });

  return results;
};

/**
 * Test login page images
 */
export const testLoginImages = (): TestResult[] => {
  const results: TestResult[] = [];

  // Test if we're on a login page
  const isLoginPage = window.location.pathname.includes('login');
  
  if (isLoginPage) {
    // Test background image loading
    const backgroundElements = document.querySelectorAll('[style*="background-image"]');
    const hasBackgroundImage = Array.from(backgroundElements).some(el => {
      const style = (el as HTMLElement).style.backgroundImage;
      return style && style !== 'none' && style.includes('url');
    });

    results.push({
      testName: 'Login Background Image',
      passed: hasBackgroundImage,
      message: hasBackgroundImage ? 'Login background image loaded' : 'Login background image not loaded',
      details: { backgroundImageFound: hasBackgroundImage, elementsChecked: backgroundElements.length }
    });

    // Test Pexels image URLs
    const pexelsImages = Array.from(backgroundElements).filter(el => {
      const style = (el as HTMLElement).style.backgroundImage;
      return style && style.includes('pexels.com');
    });

    results.push({
      testName: 'Pexels Images',
      passed: pexelsImages.length > 0,
      message: pexelsImages.length > 0 ? 'Pexels images found' : 'Pexels images not found',
      details: { pexelsImagesCount: pexelsImages.length }
    });
  } else {
    results.push({
      testName: 'Login Page Check',
      passed: false,
      message: 'Not on a login page - navigate to /beta-login or /professional-login to test',
      details: { currentPath: window.location.pathname }
    });
  }

  return results;
};

/**
 * Test theme integration
 */
export const testThemeIntegration = (): TestResult[] => {
  const results: TestResult[] = [];

  // Test AgriIntel colors
  const bodyStyles = window.getComputedStyle(document.body);
  const hasGradientBackground = bodyStyles.background.includes('gradient') || 
                               bodyStyles.backgroundImage.includes('gradient');

  results.push({
    testName: 'Gradient Background',
    passed: hasGradientBackground,
    message: hasGradientBackground ? 'Gradient background applied' : 'Gradient background not applied',
    details: { background: bodyStyles.background }
  });

  // Test glassmorphism effects
  const glassmorphElements = document.querySelectorAll('[style*="backdrop-filter"], [style*="backdropFilter"]');
  
  results.push({
    testName: 'Glassmorphism Effects',
    passed: glassmorphElements.length > 0,
    message: glassmorphElements.length > 0 ? 'Glassmorphism effects found' : 'Glassmorphism effects not found',
    details: { glassmorphElementsCount: glassmorphElements.length }
  });

  // Test AgriIntel color palette usage
  const agriIntelColors = ['#1565C0', '#2E7D32', '#F57C00'];
  const elementsWithAgriColors = Array.from(document.querySelectorAll('*')).filter(el => {
    const styles = window.getComputedStyle(el);
    return agriIntelColors.some(color => 
      styles.color.includes(color) || 
      styles.backgroundColor.includes(color) || 
      styles.borderColor.includes(color)
    );
  });

  results.push({
    testName: 'AgriIntel Color Palette',
    passed: elementsWithAgriColors.length > 0,
    message: elementsWithAgriColors.length > 0 ? 'AgriIntel colors in use' : 'AgriIntel colors not found',
    details: { elementsWithAgriColors: elementsWithAgriColors.length }
  });

  return results;
};

/**
 * Run complete BETA test suite
 */
export const runBetaTestSuite = async (): Promise<BetaTestSuite> => {
  console.log('🚀 Starting BETA Functionality Test Suite...');

  const dataPopulation = await testDataPopulation();
  const formFunctionality = testFormFunctionality();
  const loginImages = testLoginImages();
  const themeIntegration = testThemeIntegration();

  const allResults = [...dataPopulation, ...formFunctionality, ...loginImages, ...themeIntegration];
  const passedTests = allResults.filter(r => r.passed).length;
  const totalTests = allResults.length;

  const testSuite: BetaTestSuite = {
    dataPopulation,
    formFunctionality,
    loginImages,
    themeIntegration,
    overall: {
      totalTests,
      passedTests,
      failedTests: totalTests - passedTests,
      successRate: Math.round((passedTests / totalTests) * 100)
    }
  };

  console.log('✅ BETA Test Suite Complete:', testSuite.overall);
  return testSuite;
};

/**
 * Display test results in console
 */
export const displayTestResults = (testSuite: BetaTestSuite) => {
  console.group('📊 BETA Functionality Test Results');
  
  console.group('🔧 Data Population Tests');
  testSuite.dataPopulation.forEach(test => {
    console.log(`${test.passed ? '✅' : '❌'} ${test.testName}: ${test.message}`);
  });
  console.groupEnd();

  console.group('📝 Form Functionality Tests');
  testSuite.formFunctionality.forEach(test => {
    console.log(`${test.passed ? '✅' : '❌'} ${test.testName}: ${test.message}`);
  });
  console.groupEnd();

  console.group('🖼️ Login Images Tests');
  testSuite.loginImages.forEach(test => {
    console.log(`${test.passed ? '✅' : '❌'} ${test.testName}: ${test.message}`);
  });
  console.groupEnd();

  console.group('🎨 Theme Integration Tests');
  testSuite.themeIntegration.forEach(test => {
    console.log(`${test.passed ? '✅' : '❌'} ${test.testName}: ${test.message}`);
  });
  console.groupEnd();

  console.log(`\n📈 Overall Success Rate: ${testSuite.overall.successRate}% (${testSuite.overall.passedTests}/${testSuite.overall.totalTests})`);
  console.groupEnd();
};

// Export for global access
(window as any).betaTest = {
  runBetaTestSuite,
  displayTestResults,
  testDataPopulation,
  testFormFunctionality,
  testLoginImages,
  testThemeIntegration
};
