<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AgriIntel Landing Page Images Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            color: #2E7D32;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 3rem;
        }
        
        .image-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .image-card:hover {
            transform: translateY(-5px);
        }
        
        .image-preview {
            width: 100%;
            height: 200px;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            position: relative;
        }
        
        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                135deg,
                rgba(21, 101, 192, 0.85) 0%,
                rgba(46, 125, 50, 0.8) 25%,
                rgba(76, 175, 80, 0.75) 50%,
                rgba(245, 124, 0, 0.8) 75%,
                rgba(46, 125, 50, 0.85) 100%
            );
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .image-card:hover .image-overlay {
            opacity: 1;
        }
        
        .overlay-text {
            color: white;
            font-weight: bold;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }
        
        .image-info {
            padding: 15px;
        }
        
        .image-title {
            font-weight: 600;
            color: #2E7D32;
            margin-bottom: 8px;
        }
        
        .image-tab {
            background: #4CAF50;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            display: inline-block;
            margin-bottom: 8px;
        }
        
        .image-description {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .status-section {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .status-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .status-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }
        
        .status-success {
            background: #4CAF50;
        }
        
        .status-warning {
            background: #FF9800;
        }
        
        .status-error {
            background: #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 AgriIntel Landing Page Background Images Test</h1>
        
        <div class="status-section">
            <h2>Image Loading Status</h2>
            <div id="status-list">
                <!-- Status items will be populated by JavaScript -->
            </div>
        </div>
        
        <div class="image-grid" id="image-grid">
            <!-- Image cards will be populated by JavaScript -->
        </div>
    </div>

    <script>
        // Landing page images configuration
        const landingImages = [
            {
                filename: 'high-tech-pasture.jpg',
                title: 'High-Tech Pasture System',
                tab: 'Home',
                description: 'High-technology pasture grass system with mobile technology for livestock ranch'
            },
            {
                filename: 'agricultural-technology.jpg',
                title: 'Agricultural Technology',
                tab: 'Features',
                description: 'Modern agricultural technology and smart farming solutions'
            },
            {
                filename: 'iot-agriculture.png',
                title: 'IoT Agriculture',
                tab: 'Testimonials',
                description: 'IoT Agriculture technology with connected farming systems'
            },
            {
                filename: 'smart-farming-tech.png',
                title: 'Smart Farming Technology',
                tab: 'Pricing',
                description: 'Smart farming technology and precision agriculture'
            },
            {
                filename: 'environmental-assessment.jpg',
                title: 'Environmental Assessment',
                tab: 'Trust & Security',
                description: 'Environmental assessment and sustainable farming practices'
            },
            {
                filename: 'smart-farming-infographics.jpg',
                title: 'Smart Farming Infographics',
                tab: 'FAQ',
                description: 'Smart farming infographics with precision agriculture icons'
            },
            {
                filename: 'iot-in-agriculture.png',
                title: 'IoT in Agriculture',
                tab: 'Contact',
                description: 'IoT in Agriculture with connected devices and data analytics'
            }
        ];

        // Function to check if image loads successfully
        function checkImageLoad(src) {
            return new Promise((resolve) => {
                const img = new Image();
                img.onload = () => resolve(true);
                img.onerror = () => resolve(false);
                img.src = src;
            });
        }

        // Function to create image card
        function createImageCard(image, index, loaded) {
            const imagePath = `/images/landing-backgrounds/${image.filename}`;
            
            return `
                <div class="image-card">
                    <div class="image-preview" style="background-image: url('${imagePath}')">
                        <div class="image-overlay">
                            <div class="overlay-text">
                                <div>Tab ${index + 1}: ${image.tab}</div>
                                <div style="font-size: 0.8rem; margin-top: 5px;">
                                    ${loaded ? '✅ Loaded' : '❌ Failed to load'}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="image-info">
                        <div class="image-tab">${image.tab} Tab</div>
                        <div class="image-title">${image.title}</div>
                        <div class="image-description">${image.description}</div>
                    </div>
                </div>
            `;
        }

        // Function to create status item
        function createStatusItem(image, loaded) {
            const statusClass = loaded ? 'status-success' : 'status-error';
            const statusIcon = loaded ? '✓' : '✗';
            const statusText = loaded ? 'Loaded successfully' : 'Failed to load';
            
            return `
                <div class="status-item">
                    <div class="status-icon ${statusClass}">${statusIcon}</div>
                    <div>
                        <strong>${image.filename}</strong> - ${statusText}
                        <br><small>${image.tab} tab background</small>
                    </div>
                </div>
            `;
        }

        // Initialize the test
        async function initializeTest() {
            const imageGrid = document.getElementById('image-grid');
            const statusList = document.getElementById('status-list');
            
            let loadedCount = 0;
            let imageCards = '';
            let statusItems = '';
            
            for (let i = 0; i < landingImages.length; i++) {
                const image = landingImages[i];
                const imagePath = `/images/landing-backgrounds/${image.filename}`;
                const loaded = await checkImageLoad(imagePath);
                
                if (loaded) loadedCount++;
                
                imageCards += createImageCard(image, i, loaded);
                statusItems += createStatusItem(image, loaded);
            }
            
            imageGrid.innerHTML = imageCards;
            statusList.innerHTML = statusItems;
            
            // Update page title with results
            document.title = `AgriIntel Images Test - ${loadedCount}/${landingImages.length} loaded`;
            
            console.log(`Image loading test complete: ${loadedCount}/${landingImages.length} images loaded successfully`);
        }

        // Run the test when page loads
        document.addEventListener('DOMContentLoaded', initializeTest);
    </script>
</body>
</html>
