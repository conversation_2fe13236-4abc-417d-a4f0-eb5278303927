/**
 * DashboardLayout CSS Module
 * Professional AgriIntel theme with metal lime gradient backgrounds
 * Replaces inline styles for production code quality
 */

/* Enhanced AgriIntel Metal Lime Background - DRAMATIC VISIBILITY */
.agriIntelBackground {
  background: linear-gradient(135deg,
    rgba(46, 125, 50, 0.98) 0%,
    rgba(21, 101, 192, 0.95) 15%,
    rgba(245, 124, 0, 0.90) 30%,
    rgba(46, 125, 50, 0.95) 45%,
    rgba(21, 101, 192, 0.98) 60%,
    rgba(245, 124, 0, 0.90) 75%,
    rgba(46, 125, 50, 0.98) 90%,
    rgba(21, 101, 192, 0.95) 100%);
  background-size: 600% 600%;
  background-position: center;
  background-attachment: fixed;
  animation: dramaticGradientAnimation 12s ease infinite;
  box-shadow: inset 0 0 100px rgba(46, 125, 50, 0.3);
}

/* Enhanced Gradient Overlay with Animation - MORE VISIBLE */
.gradientOverlay {
  background: linear-gradient(135deg,
    rgba(46, 125, 50, 0.25) 0%,
    rgba(21, 101, 192, 0.20) 25%,
    rgba(245, 124, 0, 0.15) 50%,
    rgba(46, 125, 50, 0.20) 75%,
    rgba(21, 101, 192, 0.25) 100%);
  background-size: 300% 300%;
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  animation: overlayAnimation 8s ease infinite;
  border: 1px solid rgba(46, 125, 50, 0.1);
}

/* Enhanced Pattern Overlay */
.patternOverlay {
  background-image: url('https://www.transparenttextures.com/patterns/cubes.png');
  opacity: 0.03;
}

/* Particle Effect Overlay */
.particleOverlay {
  background: radial-gradient(circle at 10% 20%, rgba(255,255,255,0.03) 0%, transparent 20%), 
              radial-gradient(circle at 90% 80%, rgba(255,255,255,0.03) 0%, transparent 20%);
  opacity: 0.5;
}

/* Dramatic Gradient Animation Keyframes */
@keyframes dramaticGradientAnimation {
  0% {
    background-position: 0% 0%;
    transform: scale(1);
  }
  25% {
    background-position: 100% 50%;
    transform: scale(1.02);
  }
  50% {
    background-position: 50% 100%;
    transform: scale(1);
  }
  75% {
    background-position: 0% 50%;
    transform: scale(1.01);
  }
  100% {
    background-position: 0% 0%;
    transform: scale(1);
  }
}

@keyframes overlayAnimation {
  0% {
    background-position: 0% 50%;
    opacity: 0.8;
  }
  50% {
    background-position: 100% 50%;
    opacity: 1;
  }
  100% {
    background-position: 0% 50%;
    opacity: 0.8;
  }
}

/* Original gradient animation for compatibility */
@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .agriIntelBackground {
    background-attachment: scroll;
    background-size: 300% 300%;
  }
  
  .gradientOverlay {
    background-size: 150% 150%;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .agriIntelBackground {
    background: linear-gradient(135deg,
      rgba(46, 125, 50, 1) 0%,
      rgba(21, 101, 192, 1) 50%,
      rgba(46, 125, 50, 1) 100%);
  }
  
  .gradientOverlay {
    opacity: 0.8;
  }
  
  .patternOverlay {
    opacity: 0.1;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .agriIntelBackground,
  .gradientOverlay {
    animation: none;
  }
}

/* Print styles */
@media print {
  .agriIntelBackground,
  .gradientOverlay,
  .patternOverlay,
  .particleOverlay {
    background: none !important;
    opacity: 0 !important;
  }
}
