import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  Container, 
  Accordion, 
  AccordionSummary, 
  AccordionDetails 
} from '@mui/material';
import { ExpandMore } from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useLanguage } from '../../contexts/LanguageContext';

interface FAQ {
  id: string;
  question: string;
  answer: string;
}

const FAQSection: React.FC = () => {
  const { translate } = useLanguage();
  const [expanded, setExpanded] = useState<string | false>('faq-1');

  const faqs: FAQ[] = [
    {
      id: 'faq-1',
      question: 'How does AgriIntel help with livestock management in South Africa?',
      answer: 'AgriIntel is specifically designed for South African farming conditions. Our platform helps you track animal health, manage feeding schedules, monitor breeding cycles, and analyze financial performance. We understand the unique challenges of farming in SA, including seasonal variations, veld management, and compliance with local regulations.'
    },
    {
      id: 'faq-2',
      question: 'What types of livestock can I manage with AgriIntel?',
      answer: 'AgriIntel supports all major livestock types common in South Africa: cattle (beef and dairy), sheep, goats, pigs, and poultry. Our system is flexible enough to handle mixed farming operations and can track breed-specific information, growth patterns, and health requirements for each animal type.'
    },
    {
      id: 'faq-3',
      question: 'Do I need internet connection to use AgriIntel?',
      answer: 'While AgriIntel works best with internet connectivity for real-time syncing and cloud backup, our mobile app includes offline functionality. You can record animal data, health records, and feeding information even without internet, and it will sync automatically when connection is restored.'
    },
    {
      id: 'faq-4',
      question: 'How secure is my farm data on AgriIntel?',
      answer: 'Data security is our top priority. All your information is encrypted and stored on secure South African servers. We comply with POPIA (Protection of Personal Information Act) and use bank-level security measures. Your farm data belongs to you and will never be shared without your explicit consent.'
    },
    {
      id: 'faq-5',
      question: 'Can AgriIntel help with compliance and record-keeping?',
      answer: 'Yes! AgriIntel automatically generates the records required for various compliance purposes including veterinary inspections, organic certification, and export documentation. Our system tracks vaccination schedules, treatment records, and movement certificates to help you stay compliant with South African agricultural regulations.'
    },
    {
      id: 'faq-6',
      question: 'What support is available for new users?',
      answer: 'We provide comprehensive support including video tutorials in English and Afrikaans, phone support during business hours, and on-farm training for Professional subscribers. Our support team understands South African farming practices and can help you optimize AgriIntel for your specific operation.'
    },
    {
      id: 'faq-7',
      question: 'How does the pricing work and can I upgrade later?',
      answer: 'Start with our free BETA plan (up to 50 animals) to test the system. You can upgrade to Professional (R699/month) anytime for unlimited animals and advanced features. There are no setup fees or long-term contracts - you can cancel or downgrade at any time.'
    },
    {
      id: 'faq-8',
      question: 'Does AgriIntel integrate with other farm management tools?',
      answer: 'AgriIntel can export data in various formats (CSV, Excel, PDF) for use with accounting software, veterinary systems, and other farm management tools. Our Professional plan includes API access for custom integrations with existing farm systems.'
    }
  ];

  const handleChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpanded(isExpanded ? panel : false);
  };

  return (
    <Box
      component="section"
      sx={{
        py: { xs: 8, md: 12 },
        background: 'linear-gradient(135deg, #F8F9FA 0%, #FFFFFF 100%)',
        position: 'relative'
      }}
    >
      <Container maxWidth="md">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <Box textAlign="center" mb={8}>
            <Typography
              variant="h2"
              component="h2"
              sx={{
                fontSize: { xs: '2rem', md: '2.5rem' },
                fontWeight: 700,
                color: '#2E7D32',
                mb: 2
              }}
            >
              {translate('landing.faq.title', { fallback: 'Frequently Asked Questions' })}
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: '#666',
                maxWidth: '600px',
                mx: 'auto',
                lineHeight: 1.6
              }}
            >
              {translate('landing.faq.subtitle', { 
                fallback: 'Get answers to common questions about livestock management with AgriIntel' 
              })}
            </Typography>
          </Box>
        </motion.div>

        {/* FAQ Accordion */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <Box>
            {faqs.map((faq, index) => (
              <Accordion
                key={faq.id}
                expanded={expanded === faq.id}
                onChange={handleChange(faq.id)}
                sx={{
                  mb: 2,
                  borderRadius: 2,
                  border: '1px solid rgba(46, 125, 50, 0.1)',
                  boxShadow: 'none',
                  '&:before': {
                    display: 'none'
                  },
                  '&.Mui-expanded': {
                    margin: '0 0 16px 0',
                    boxShadow: '0 8px 24px rgba(46, 125, 50, 0.1)'
                  }
                }}
              >
                <AccordionSummary
                  expandIcon={<ExpandMore sx={{ color: '#4CAF50' }} />}
                  sx={{
                    backgroundColor: expanded === faq.id ? 'rgba(46, 125, 50, 0.05)' : 'transparent',
                    borderRadius: 2,
                    '&:hover': {
                      backgroundColor: 'rgba(46, 125, 50, 0.05)'
                    },
                    '& .MuiAccordionSummary-content': {
                      margin: '16px 0'
                    }
                  }}
                >
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 600,
                      color: '#2E7D32',
                      fontSize: { xs: '1rem', md: '1.1rem' }
                    }}
                  >
                    {faq.question}
                  </Typography>
                </AccordionSummary>
                <AccordionDetails
                  sx={{
                    pt: 0,
                    pb: 3,
                    px: 3
                  }}
                >
                  <Typography
                    variant="body1"
                    sx={{
                      color: '#666',
                      lineHeight: 1.7,
                      fontSize: '1rem'
                    }}
                  >
                    {faq.answer}
                  </Typography>
                </AccordionDetails>
              </Accordion>
            ))}
          </Box>
        </motion.div>

        {/* Contact CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <Box
            sx={{
              textAlign: 'center',
              mt: 8,
              p: 4,
              background: 'linear-gradient(135deg, rgba(46, 125, 50, 0.05) 0%, rgba(76, 175, 80, 0.05) 100%)',
              borderRadius: 3,
              border: '1px solid rgba(46, 125, 50, 0.1)'
            }}
          >
            <Typography
              variant="h5"
              sx={{
                fontWeight: 600,
                color: '#2E7D32',
                mb: 2
              }}
            >
              Still have questions?
            </Typography>
            <Typography
              variant="body1"
              sx={{
                color: '#666',
                mb: 3,
                lineHeight: 1.6
              }}
            >
              Our team of livestock management experts is here to help you get the most out of AgriIntel.
            </Typography>
            <Box
              sx={{
                display: 'flex',
                flexDirection: { xs: 'column', sm: 'row' },
                gap: 2,
                justifyContent: 'center',
                alignItems: 'center'
              }}
            >
              <Typography
                variant="body2"
                sx={{
                  color: '#4CAF50',
                  fontWeight: 600
                }}
              >
                📞 Support: +27 11 123 4567
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: '#4CAF50',
                  fontWeight: 600
                }}
              >
                📧 Email: <EMAIL>
              </Typography>
            </Box>
          </Box>
        </motion.div>
      </Container>
    </Box>
  );
};

export default FAQSection;
