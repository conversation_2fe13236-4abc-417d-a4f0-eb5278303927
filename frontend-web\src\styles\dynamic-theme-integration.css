/* Dynamic Theme Integration - Ensures Complete Theme Coverage */

/* Theme-aware component classes */
.agri-intel-app {
  background: var(--primary-gradient) !important;
  background-attachment: fixed !important;
  min-height: 100vh !important;
  color: #ffffff !important;
  font-family: "<PERSON>", "Roboto", "Helvetica", "Arial", sans-serif !important;
}

/* Dynamic theme classes for each variant */
.theme-nature {
  --primary-color: #2E7D32;
  --primary-color-light: #4CAF50;
  --primary-color-dark: #1B5E20;
  --secondary-color: #FF8F00;
  --primary-gradient: linear-gradient(135deg, #2E7D32 0%, #4CAF50 50%, #81C784 100%);
}

.theme-earth {
  --primary-color: #8D6E63;
  --primary-color-light: #A1887F;
  --primary-color-dark: #5D4037;
  --secondary-color: #FF8F00;
  --primary-gradient: linear-gradient(135deg, #8D6E63 0%, #A1887F 50%, #BCAAA4 100%);
}

.theme-sky {
  --primary-color: #1976D2;
  --primary-color-light: #42A5F5;
  --primary-color-dark: #0D47A1;
  --secondary-color: #FF8F00;
  --primary-gradient: linear-gradient(135deg, #1976D2 0%, #42A5F5 50%, #90CAF9 100%);
}

.theme-sunset {
  --primary-color: #FF8F00;
  --primary-color-light: #FFB74D;
  --primary-color-dark: #E65100;
  --secondary-color: #2E7D32;
  --primary-gradient: linear-gradient(135deg, #FF8F00 0%, #FFB74D 50%, #FFCC02 100%);
}

.theme-forest {
  --primary-color: #388E3C;
  --primary-color-light: #66BB6A;
  --primary-color-dark: #2E7D32;
  --secondary-color: #8D6E63;
  --primary-gradient: linear-gradient(135deg, #388E3C 0%, #66BB6A 50%, #A5D6A7 100%);
}

.theme-professional {
  --primary-color: #1565C0;
  --primary-color-light: #42A5F5;
  --primary-color-dark: #0D47A1;
  --secondary-color: #2E7D32;
  --primary-gradient: linear-gradient(135deg, #1565C0 0%, #1976D2 50%, #42A5F5 100%);
}

.theme-warm {
  --primary-color: #F57C00;
  --primary-color-light: #FFB74D;
  --primary-color-dark: #E65100;
  --secondary-color: #FF5722;
  --primary-gradient: linear-gradient(135deg, #F57C00 0%, #FF9800 50%, #FFB74D 100%);
}

.theme-modern {
  --primary-color: #6A1B9A;
  --primary-color-light: #AB47BC;
  --primary-color-dark: #4A148C;
  --secondary-color: #E91E63;
  --primary-gradient: linear-gradient(135deg, #6A1B9A 0%, #8E24AA 50%, #AB47BC 100%);
}

.theme-ocean {
  --primary-color: #0277BD;
  --primary-color-light: #29B6F6;
  --primary-color-dark: #01579B;
  --secondary-color: #00BCD4;
  --primary-gradient: linear-gradient(135deg, #0277BD 0%, #0288D1 50%, #29B6F6 100%);
}

.theme-crimson {
  --primary-color: #C62828;
  --primary-color-light: #EF5350;
  --primary-color-dark: #B71C1C;
  --secondary-color: #FF7043;
  --primary-gradient: linear-gradient(135deg, #C62828 0%, #E53935 50%, #EF5350 100%);
}

.theme-purple {
  --primary-color: #7B1FA2;
  --primary-color-light: #BA68C8;
  --primary-color-dark: #4A148C;
  --secondary-color: #FF4081;
  --primary-gradient: linear-gradient(135deg, #7B1FA2 0%, #9C27B0 50%, #BA68C8 100%);
}

.theme-teal {
  --primary-color: #00695C;
  --primary-color-light: #26A69A;
  --primary-color-dark: #004D40;
  --secondary-color: #FF7043;
  --primary-gradient: linear-gradient(135deg, #00695C 0%, #26A69A 50%, #80CBC4 100%);
}

/* Apply theme to body when theme changes */
body.agri-intel-app {
  background: var(--primary-gradient) !important;
  background-attachment: fixed !important;
  transition: background 0.5s ease !important;
}

/* Dynamic component styling */
.agri-card,
.module-card,
.dashboard-card,
.theme-card {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 16px !important;
  color: #ffffff !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}

.agri-card:hover,
.module-card:hover,
.dashboard-card:hover,
.theme-card:hover {
  transform: translateY(-4px) !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4) !important;
  background: rgba(255, 255, 255, 0.15) !important;
  border-color: var(--primary-color) !important;
}

/* Dynamic button styling */
.agri-button-primary,
.theme-button-primary {
  background: var(--primary-gradient) !important;
  color: #ffffff !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 12px !important;
  padding: 12px 24px !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
  text-transform: none !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3) !important;
  cursor: pointer !important;
}

.agri-button-primary:hover,
.theme-button-primary:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4) !important;
  filter: brightness(1.1) !important;
}

.agri-button-secondary,
.theme-button-secondary {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 12px !important;
  padding: 12px 24px !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
  text-transform: none !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  cursor: pointer !important;
}

.agri-button-secondary:hover,
.theme-button-secondary:hover {
  border-color: var(--primary-color) !important;
  background: rgba(255, 255, 255, 0.15) !important;
  transform: translateY(-2px) !important;
}

/* Dynamic input styling */
.agri-input,
.theme-input {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 12px !important;
  padding: 12px 16px !important;
  color: #ffffff !important;
  font-size: 1rem !important;
  transition: all 0.3s ease !important;
}

.agri-input:focus,
.theme-input:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 3px var(--primary-color-alpha, rgba(46, 125, 50, 0.3)) !important;
  outline: none !important;
}

.agri-input::placeholder,
.theme-input::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}

/* Dynamic header styling */
.agri-header,
.theme-header {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 16px !important;
  padding: 16px 24px !important;
  color: #ffffff !important;
  margin-bottom: 24px !important;
}

/* Module-specific dynamic styling */
.module-animals { border-color: var(--primary-color) !important; }
.module-health { border-color: #E53E3E !important; }
.module-feeding { border-color: #38A169 !important; }
.module-financial { border-color: #3182CE !important; }
.module-breeding { border-color: #805AD5 !important; }
.module-inventory { border-color: #D69E2E !important; }
.module-commercial { border-color: #00B5D8 !important; }
.module-reports { border-color: #DD6B20 !important; }
.module-resources { border-color: #319795 !important; }
.module-settings { border-color: #718096 !important; }
.module-compliance { border-color: #9F7AEA !important; }

/* Navigation styling */
.agri-nav,
.theme-nav {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: #ffffff !important;
}

.agri-nav-item,
.theme-nav-item {
  color: rgba(255, 255, 255, 0.8) !important;
  transition: all 0.3s ease !important;
}

.agri-nav-item:hover,
.theme-nav-item:hover,
.agri-nav-item.active,
.theme-nav-item.active {
  color: #ffffff !important;
  background: var(--primary-color) !important;
  border-radius: 8px !important;
}

/* Ensure all text is visible */
.agri-intel-app * {
  color: #ffffff !important;
}

/* Override any remaining light backgrounds */
.agri-intel-app .MuiPaper-root,
.agri-intel-app .MuiCard-root,
.agri-intel-app .MuiDialog-paper,
.agri-intel-app .MuiDrawer-paper,
.agri-intel-app .MuiAppBar-root {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: #ffffff !important;
}

/* Animation for theme transitions */
.theme-transition {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Responsive theme adjustments */
@media (max-width: 768px) {
  .agri-card,
  .module-card,
  .dashboard-card,
  .theme-card {
    margin: 8px !important;
    padding: 16px !important;
  }
  
  .agri-button-primary,
  .agri-button-secondary,
  .theme-button-primary,
  .theme-button-secondary {
    padding: 10px 20px !important;
    font-size: 0.9rem !important;
  }
}
