import React, { useEffect, useRef, useState } from 'react';
import { Box } from '@mui/material';
import '../../styles/neural-network-background.css';

interface Node {
  x: number;
  y: number;
  vx: number;
  vy: number;
  connections: number[];
}

interface NeuralNetworkBackgroundProps {
  nodeCount?: number;
  connectionDistance?: number;
  animationSpeed?: number;
  opacity?: number;
}

const NeuralNetworkBackground: React.FC<NeuralNetworkBackgroundProps> = ({
  nodeCount = 50,
  connectionDistance = 150,
  animationSpeed = 0.5,
  opacity = 0.6
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const nodesRef = useRef<Node[]>([]);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

  // Initialize nodes
  const initializeNodes = (width: number, height: number) => {
    const nodes: Node[] = [];
    for (let i = 0; i < nodeCount; i++) {
      nodes.push({
        x: Math.random() * width,
        y: Math.random() * height,
        vx: (Math.random() - 0.5) * animationSpeed,
        vy: (Math.random() - 0.5) * animationSpeed,
        connections: []
      });
    }
    nodesRef.current = nodes;
  };

  // Calculate connections between nodes
  const calculateConnections = () => {
    const nodes = nodesRef.current;
    nodes.forEach((node, i) => {
      node.connections = [];
      nodes.forEach((otherNode, j) => {
        if (i !== j) {
          const distance = Math.sqrt(
            Math.pow(node.x - otherNode.x, 2) + Math.pow(node.y - otherNode.y, 2)
          );
          if (distance < connectionDistance) {
            node.connections.push(j);
          }
        }
      });
    });
  };

  // Update node positions
  const updateNodes = (width: number, height: number) => {
    const nodes = nodesRef.current;
    nodes.forEach(node => {
      node.x += node.vx;
      node.y += node.vy;

      // Bounce off edges
      if (node.x <= 0 || node.x >= width) node.vx *= -1;
      if (node.y <= 0 || node.y >= height) node.vy *= -1;

      // Keep within bounds
      node.x = Math.max(0, Math.min(width, node.x));
      node.y = Math.max(0, Math.min(height, node.y));
    });
  };

  // Draw the neural network
  const draw = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    const nodes = nodesRef.current;

    // Draw connections
    ctx.strokeStyle = `rgba(0, 212, 255, ${opacity * 0.3})`;
    ctx.lineWidth = 1;
    nodes.forEach((node, i) => {
      node.connections.forEach(connectionIndex => {
        const connectedNode = nodes[connectionIndex];
        if (connectedNode) {
          const distance = Math.sqrt(
            Math.pow(node.x - connectedNode.x, 2) + Math.pow(node.y - connectedNode.y, 2)
          );
          const alpha = Math.max(0, 1 - distance / connectionDistance);
          
          ctx.strokeStyle = `rgba(0, 212, 255, ${alpha * opacity * 0.4})`;
          ctx.beginPath();
          ctx.moveTo(node.x, node.y);
          ctx.lineTo(connectedNode.x, connectedNode.y);
          ctx.stroke();
        }
      });
    });

    // Draw nodes
    nodes.forEach((node, i) => {
      const connectionCount = node.connections.length;
      const nodeSize = Math.max(2, Math.min(6, connectionCount * 0.5));
      
      // Node glow effect
      const gradient = ctx.createRadialGradient(node.x, node.y, 0, node.x, node.y, nodeSize * 3);
      gradient.addColorStop(0, `rgba(0, 212, 255, ${opacity})`);
      gradient.addColorStop(0.5, `rgba(0, 212, 255, ${opacity * 0.5})`);
      gradient.addColorStop(1, 'rgba(0, 212, 255, 0)');
      
      ctx.fillStyle = gradient;
      ctx.beginPath();
      ctx.arc(node.x, node.y, nodeSize * 3, 0, Math.PI * 2);
      ctx.fill();

      // Node core
      ctx.fillStyle = `rgba(0, 212, 255, ${opacity})`;
      ctx.beginPath();
      ctx.arc(node.x, node.y, nodeSize, 0, Math.PI * 2);
      ctx.fill();

      // Pulse effect for highly connected nodes
      if (connectionCount > 3) {
        const pulseSize = nodeSize + Math.sin(Date.now() * 0.005 + i) * 2;
        ctx.strokeStyle = `rgba(124, 58, 237, ${opacity * 0.6})`;
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.arc(node.x, node.y, pulseSize, 0, Math.PI * 2);
        ctx.stroke();
      }
    });
  };

  // Animation loop
  const animate = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const { width, height } = dimensions;
    
    updateNodes(width, height);
    calculateConnections();
    draw(ctx, width, height);

    animationRef.current = requestAnimationFrame(animate);
  };

  // Handle resize
  const handleResize = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const width = window.innerWidth;
    const height = window.innerHeight;

    canvas.width = width;
    canvas.height = height;
    
    setDimensions({ width, height });
    initializeNodes(width, height);
  };

  useEffect(() => {
    handleResize();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (dimensions.width > 0 && dimensions.height > 0) {
      animate();
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [dimensions]);

  return (
    <Box className="neural-network-container">
      <canvas
        ref={canvasRef}
        className="neural-network-canvas"
      />
    </Box>
  );
};

export default NeuralNetworkBackground;
