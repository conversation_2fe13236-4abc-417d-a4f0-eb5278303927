import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import {
  Ty<PERSON>graphy,
  TextField,
  Button,
  Alert,
  InputAdornment,
  IconButton,
  CircularProgress,
  Box,
  Card,
  CardContent,
  Container,
  Chip,
  Stack,
  Grid
} from '@mui/material';
import {
  Person,
  Lock,
  Visibility,
  VisibilityOff,
  Agriculture,
  Verified,
  Analytics,
  TrendingUp,
  Security,
  CloudSync
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import EnhancedLanguageSelector from '../common/EnhancedLanguageSelector';
import '../../styles/professional-tier.css';

const ProfessionalLogin: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');

  const { login, isLoading, error: authError } = useAuth();
  const { translate } = useLanguage();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (authError) {
      setError(authError);
    }
  }, [authError]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!username || !password) {
      setError('Please enter both username and password');
      return;
    }
    try {
      await login(username, password);
      
      // Get user from localStorage after successful login
      const userData = JSON.parse(localStorage.getItem('user') || '{}');
      const redirectPath = location.state?.from?.pathname || '/dashboard';

      // Redirect based on user role
      if (userData.role === 'professional' || userData.role === 'admin') {
        navigate('/dashboard', { replace: true });
      } else {
        navigate(redirectPath, { replace: true });
      }
    } catch (err) {
      setError('Invalid credentials. Please try again.');
    }
  };

  const handleTogglePassword = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="pro-login-container">
      {/* Language Selector */}
      <Box sx={{ position: 'absolute', top: 20, right: 20, zIndex: 1000 }}>
        <EnhancedLanguageSelector
          variant="futuristic"
          showLabel={false}
          showFlag={true}
          size="medium"
        />
      </Box>

      <Container maxWidth="md" sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        py: 4,
        position: 'relative',
        zIndex: 1
      }}>
        <Grid container spacing={4} alignItems="center">
          {/* Left Side - Features */}
          <Grid item xs={12} md={6}>
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Box sx={{ color: 'white', mb: 4 }}>
                <Chip 
                  label="Pro V2" 
                  className="pro-brand-badge"
                  icon={<Verified />}
                  sx={{ mb: 2 }}
                />
                <Typography variant="h3" sx={{ fontWeight: 800, mb: 2 }}>
                  Professional Livestock Management
                </Typography>
                <Typography variant="h6" sx={{ opacity: 0.9, mb: 3 }}>
                  Complete farm management solution with AI-powered insights
                </Typography>
              </Box>

              <Stack spacing={2}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'white' }}>
                  <Analytics sx={{ color: '#8BC34A' }} />
                  <Typography variant="body1">Advanced Analytics & Reporting</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'white' }}>
                  <TrendingUp sx={{ color: '#8BC34A' }} />
                  <Typography variant="body1">Predictive Health Monitoring</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'white' }}>
                  <Security sx={{ color: '#8BC34A' }} />
                  <Typography variant="body1">Enterprise-Grade Security</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'white' }}>
                  <CloudSync sx={{ color: '#8BC34A' }} />
                  <Typography variant="body1">Real-time Data Synchronization</Typography>
                </Box>
              </Stack>
            </motion.div>
          </Grid>

          {/* Right Side - Login Form */}
          <Grid item xs={12} md={6}>
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Card className="pro-login-card">
                <CardContent sx={{ p: 4 }}>
                  {/* Professional Branding */}
                  <Box sx={{ textAlign: 'center', mb: 3 }}>
                    <Typography variant="h4" className="pro-login-title">
                      Professional Access
                    </Typography>
                    <Typography variant="body1" className="pro-login-subtitle">
                      Sign in to your Pro V2 account
                    </Typography>
                  </Box>

                  {/* Login Form */}
                  <Box component="form" onSubmit={handleLogin} sx={{ width: '100%' }}>
                    {error && (
                      <Alert severity="error" sx={{ mb: 2 }}>
                        {error}
                      </Alert>
                    )}

                    <TextField
                      fullWidth
                      label="Username"
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                      margin="normal"
                      required
                      autoComplete="username"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Person sx={{ color: '#4CAF50' }} />
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          '&:hover fieldset': {
                            borderColor: '#4CAF50',
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: '#4CAF50',
                          },
                        },
                        '& .MuiInputLabel-root.Mui-focused': {
                          color: '#4CAF50',
                        },
                      }}
                    />

                    <TextField
                      fullWidth
                      label="Password"
                      type={showPassword ? 'text' : 'password'}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      margin="normal"
                      required
                      autoComplete="current-password"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Lock sx={{ color: '#4CAF50' }} />
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              aria-label="toggle password visibility"
                              onClick={handleTogglePassword}
                              edge="end"
                              sx={{ color: '#4CAF50' }}
                            >
                              {showPassword ? <VisibilityOff /> : <Visibility />}
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          '&:hover fieldset': {
                            borderColor: '#4CAF50',
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: '#4CAF50',
                          },
                        },
                        '& .MuiInputLabel-root.Mui-focused': {
                          color: '#4CAF50',
                        },
                      }}
                    />

                    <Button
                      type="submit"
                      fullWidth
                      variant="contained"
                      disabled={isLoading}
                      className="pro-login-button"
                      sx={{ mt: 3, mb: 2, py: 1.5 }}
                      startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : <Agriculture />}
                    >
                      {isLoading ? 'Signing In...' : 'Access Professional Dashboard'}
                    </Button>
                  </Box>

                  {/* Demo Credentials */}
                  <Box sx={{ mt: 3, p: 2, bgcolor: 'rgba(76, 175, 80, 0.1)', borderRadius: 2, border: '1px solid #66BB6A' }}>
                    <Typography variant="body2" sx={{ color: '#2E7D32', fontWeight: 600, mb: 1 }}>
                      🔑 Demo Credentials:
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#2E7D32' }}>
                      Professional: <strong>Pro</strong> / <strong>123</strong>
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#2E7D32' }}>
                      Administrator: <strong>admin</strong> / <strong>Admin@123</strong>
                    </Typography>
                  </Box>

                  {/* Professional Features */}
                  <Box sx={{ mt: 3, p: 2, bgcolor: '#E8F5E8', borderRadius: 2 }}>
                    <Typography variant="body2" sx={{ color: '#2E7D32', fontWeight: 600, mb: 1 }}>
                      ✨ Pro V2 Features:
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#2E7D32', fontSize: '0.8rem' }}>
                      • All 12 modules with full functionality
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#2E7D32', fontSize: '0.8rem' }}>
                      • AI-powered predictive analytics
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#2E7D32', fontSize: '0.8rem' }}>
                      • Professional marketplace access
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#2E7D32', fontSize: '0.8rem' }}>
                      • Priority support & training
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        </Grid>
      </Container>
    </div>
  );
};

export default ProfessionalLogin;
