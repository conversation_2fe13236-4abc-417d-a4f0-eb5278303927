import React from 'react';
import { 
  Box, 
  Typography, 
  Container, 
  Card, 
  CardContent, 
  Button, 
  List, 
  ListItem, 
  ListItemIcon, 
  ListItemText,
  Chip
} from '@mui/material';
import { 
  Check, 
  Close, 
  Star, 
  Verified, 
  TrendingUp,
  Agriculture 
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../../contexts/LanguageContext';

interface PricingFeature {
  name: string;
  beta: boolean | string;
  professional: boolean | string;
}

const PricingSection: React.FC = () => {
  const navigate = useNavigate();
  const { translate } = useLanguage();

  const features: PricingFeature[] = [
    { name: 'Animal Management', beta: 'Up to 50 animals', professional: 'Unlimited animals' },
    { name: 'Health Records & Tracking', beta: true, professional: true },
    { name: 'Feeding Management', beta: 'Basic plans', professional: 'Advanced nutrition analysis' },
    { name: 'Financial Overview', beta: 'Basic reports', professional: 'Advanced analytics' },
    { name: 'Mobile App Access', beta: true, professional: true },
    { name: 'Data Export', beta: 'CSV only', professional: 'Multiple formats' },
    { name: 'Breeding Management', beta: false, professional: true },
    { name: 'Inventory Management', beta: false, professional: true },
    { name: 'AI-Powered Analytics', beta: false, professional: true },
    { name: 'Professional Marketplace', beta: false, professional: true },
    { name: 'Priority Support', beta: false, professional: true },
    { name: 'Custom Reports', beta: false, professional: true },
    { name: 'Multi-User Access', beta: false, professional: 'Up to 5 users' },
    { name: 'API Access', beta: false, professional: true },
    { name: 'Compliance Management', beta: false, professional: true }
  ];

  const handlePlanSelect = (plan: 'beta' | 'professional') => {
    if (plan === 'beta') {
      navigate('/login?tier=beta');
    } else {
      navigate('/login?tier=professional');
    }
  };

  return (
    <Box
      component="section"
      sx={{
        py: { xs: 8, md: 12 },
        background: 'linear-gradient(135deg, #FFFFFF 0%, #F8F9FA 100%)',
        position: 'relative'
      }}
    >
      <Container maxWidth="lg">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <Box textAlign="center" mb={8}>
            <Typography
              variant="h2"
              component="h2"
              sx={{
                fontSize: { xs: '2rem', md: '2.5rem' },
                fontWeight: 700,
                color: '#2E7D32',
                mb: 2
              }}
            >
              {translate('landing.pricing.title', { fallback: 'Choose Your Plan' })}
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: '#666',
                maxWidth: '600px',
                mx: 'auto',
                lineHeight: 1.6
              }}
            >
              {translate('landing.pricing.subtitle', { 
                fallback: 'Start with our free BETA plan or upgrade to Professional for advanced features' 
              })}
            </Typography>
          </Box>
        </motion.div>

        {/* Pricing Cards */}
        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' },
            gap: 4,
            mb: 8
          }}
        >
          {/* BETA Plan */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <Card
              sx={{
                height: '100%',
                border: '2px solid #FF9800',
                borderRadius: 3,
                position: 'relative',
                background: 'linear-gradient(135deg, #FFF8E1 0%, #FFFFFF 100%)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 20px 40px rgba(255, 152, 0, 0.15)'
                }
              }}
            >
              <CardContent sx={{ p: 4, textAlign: 'center' }}>
                {/* Plan Badge */}
                <Chip
                  label="BETA V1 Starter"
                  icon={<Star />}
                  sx={{
                    background: 'linear-gradient(135deg, #FF9800, #FFA726)',
                    color: 'white',
                    fontWeight: 700,
                    mb: 3
                  }}
                />

                {/* Price */}
                <Typography
                  variant="h3"
                  sx={{
                    fontSize: '3rem',
                    fontWeight: 800,
                    color: '#E65100',
                    mb: 1
                  }}
                >
                  FREE
                </Typography>
                <Typography
                  variant="body1"
                  sx={{ color: '#666', mb: 3 }}
                >
                  30-day trial • No credit card required
                </Typography>

                {/* CTA Button */}
                <Button
                  variant="contained"
                  size="large"
                  onClick={() => handlePlanSelect('beta')}
                  sx={{
                    background: 'linear-gradient(135deg, #FF9800, #FFA726)',
                    color: 'white',
                    py: 1.5,
                    px: 4,
                    mb: 4,
                    fontWeight: 600,
                    '&:hover': {
                      background: 'linear-gradient(135deg, #F57C00, #FF9800)'
                    }
                  }}
                  startIcon={<Agriculture />}
                >
                  Start Free Trial
                </Button>

                {/* Features */}
                <Box textAlign="left">
                  <Typography
                    variant="h6"
                    sx={{ color: '#E65100', fontWeight: 600, mb: 2 }}
                  >
                    Perfect for small farms:
                  </Typography>
                  <List dense>
                    <ListItem sx={{ py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <Check sx={{ color: '#FF9800', fontSize: 20 }} />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Up to 50 animals"
                        primaryTypographyProps={{ fontSize: '0.9rem' }}
                      />
                    </ListItem>
                    <ListItem sx={{ py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <Check sx={{ color: '#FF9800', fontSize: 20 }} />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Basic health monitoring"
                        primaryTypographyProps={{ fontSize: '0.9rem' }}
                      />
                    </ListItem>
                    <ListItem sx={{ py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <Check sx={{ color: '#FF9800', fontSize: 20 }} />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Feed tracking & planning"
                        primaryTypographyProps={{ fontSize: '0.9rem' }}
                      />
                    </ListItem>
                    <ListItem sx={{ py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <Check sx={{ color: '#FF9800', fontSize: 20 }} />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Financial overview"
                        primaryTypographyProps={{ fontSize: '0.9rem' }}
                      />
                    </ListItem>
                    <ListItem sx={{ py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <Check sx={{ color: '#FF9800', fontSize: 20 }} />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Mobile app access"
                        primaryTypographyProps={{ fontSize: '0.9rem' }}
                      />
                    </ListItem>
                  </List>
                </Box>
              </CardContent>
            </Card>
          </motion.div>

          {/* Professional Plan */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <Card
              sx={{
                height: '100%',
                border: '2px solid #4CAF50',
                borderRadius: 3,
                position: 'relative',
                background: 'linear-gradient(135deg, #E8F5E8 0%, #FFFFFF 100%)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 20px 40px rgba(76, 175, 80, 0.15)'
                }
              }}
            >
              {/* Popular Badge */}
              <Box
                sx={{
                  position: 'absolute',
                  top: -12,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  background: 'linear-gradient(135deg, #4CAF50, #66BB6A)',
                  color: 'white',
                  px: 3,
                  py: 1,
                  borderRadius: 20,
                  fontSize: '0.875rem',
                  fontWeight: 600
                }}
              >
                Most Popular
              </Box>

              <CardContent sx={{ p: 4, textAlign: 'center' }}>
                {/* Plan Badge */}
                <Chip
                  label="Professional V2"
                  icon={<Verified />}
                  sx={{
                    background: 'linear-gradient(135deg, #4CAF50, #66BB6A)',
                    color: 'white',
                    fontWeight: 700,
                    mb: 3
                  }}
                />

                {/* Price */}
                <Typography
                  variant="h3"
                  sx={{
                    fontSize: '3rem',
                    fontWeight: 800,
                    color: '#2E7D32',
                    mb: 1
                  }}
                >
                  R699
                </Typography>
                <Typography
                  variant="body1"
                  sx={{ color: '#666', mb: 3 }}
                >
                  per month • Cancel anytime
                </Typography>

                {/* CTA Button */}
                <Button
                  variant="contained"
                  size="large"
                  onClick={() => handlePlanSelect('professional')}
                  sx={{
                    background: 'linear-gradient(135deg, #4CAF50, #66BB6A)',
                    color: 'white',
                    py: 1.5,
                    px: 4,
                    mb: 4,
                    fontWeight: 600,
                    '&:hover': {
                      background: 'linear-gradient(135deg, #388E3C, #4CAF50)'
                    }
                  }}
                  startIcon={<TrendingUp />}
                >
                  Go Professional
                </Button>

                {/* Features */}
                <Box textAlign="left">
                  <Typography
                    variant="h6"
                    sx={{ color: '#2E7D32', fontWeight: 600, mb: 2 }}
                  >
                    Everything in BETA, plus:
                  </Typography>
                  <List dense>
                    <ListItem sx={{ py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <Check sx={{ color: '#4CAF50', fontSize: 20 }} />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Unlimited animals"
                        primaryTypographyProps={{ fontSize: '0.9rem' }}
                      />
                    </ListItem>
                    <ListItem sx={{ py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <Check sx={{ color: '#4CAF50', fontSize: 20 }} />
                      </ListItemIcon>
                      <ListItemText 
                        primary="AI-powered analytics"
                        primaryTypographyProps={{ fontSize: '0.9rem' }}
                      />
                    </ListItem>
                    <ListItem sx={{ py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <Check sx={{ color: '#4CAF50', fontSize: 20 }} />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Professional marketplace"
                        primaryTypographyProps={{ fontSize: '0.9rem' }}
                      />
                    </ListItem>
                    <ListItem sx={{ py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <Check sx={{ color: '#4CAF50', fontSize: 20 }} />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Priority support & training"
                        primaryTypographyProps={{ fontSize: '0.9rem' }}
                      />
                    </ListItem>
                    <ListItem sx={{ py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <Check sx={{ color: '#4CAF50', fontSize: 20 }} />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Multi-user access (5 users)"
                        primaryTypographyProps={{ fontSize: '0.9rem' }}
                      />
                    </ListItem>
                  </List>
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Box>
      </Container>
    </Box>
  );
};

export default PricingSection;
