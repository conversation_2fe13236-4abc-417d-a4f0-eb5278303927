/**
 * Automated Lifecycle Management Service
 * 
 * This service handles automated animal lifecycle transitions, filtering,
 * categorization, and inventory transfers based on age and other criteria.
 */

const Animal = require('../models/schemas/animal.schema');
const logger = require('../utils/logger');

class LifecycleManagementService {
  constructor() {
    this.lifecycleStages = {
      calf: { minMonths: 0, maxMonths: 6, description: 'Newborn to 6 months' },
      juvenile: { minMonths: 6, maxMonths: 12, description: '6 months to 1 year' },
      adult: { minMonths: 12, maxMonths: 96, description: '1 year to 8 years' },
      breeding: { minMonths: 18, maxMonths: 84, description: 'Prime breeding age' },
      retirement: { minMonths: 96, maxMonths: null, description: '8+ years, retirement age' }
    };

    this.transferCriteria = {
      retirement: {
        age: 10, // years
        breedingCycles: 8,
        productivityThreshold: 0.7
      },
      sale: {
        marketWeight: {
          cattle: 450, // kg
          sheep: 45,
          goat: 35,
          pig: 100
        },
        optimalAge: {
          cattle: 24, // months
          sheep: 12,
          goat: 12,
          pig: 6
        }
      },
      deceased: {
        immediate: true
      }
    };
  }

  /**
   * Calculate animal age in months
   */
  calculateAgeInMonths(birthDate) {
    if (!birthDate) return null;
    
    const now = new Date();
    const birth = new Date(birthDate);
    const months = (now.getFullYear() - birth.getFullYear()) * 12 + (now.getMonth() - birth.getMonth());
    return Math.max(0, months);
  }

  /**
   * Determine expected lifecycle stage based on age
   */
  getExpectedLifecycleStage(ageInMonths, species = 'cattle') {
    if (ageInMonths === null) return 'unknown';

    // Species-specific adjustments
    const adjustments = {
      cattle: { multiplier: 1.0 },
      sheep: { multiplier: 0.8 },
      goat: { multiplier: 0.8 },
      pig: { multiplier: 0.5 },
      chicken: { multiplier: 0.1 }
    };

    const adjustedAge = ageInMonths * (adjustments[species]?.multiplier || 1.0);

    for (const [stage, criteria] of Object.entries(this.lifecycleStages)) {
      if (adjustedAge >= criteria.minMonths && 
          (criteria.maxMonths === null || adjustedAge < criteria.maxMonths)) {
        return stage;
      }
    }

    return 'retirement';
  }

  /**
   * Check if animal should be transferred to inventory
   */
  shouldTransferToInventory(animal) {
    const ageInMonths = this.calculateAgeInMonths(animal.birthDate);
    const ageInYears = ageInMonths ? ageInMonths / 12 : 0;

    // Check deceased status
    if (animal.status === 'deceased' || animal.healthStatus === 'deceased') {
      return {
        shouldTransfer: true,
        reason: 'deceased',
        priority: 'immediate',
        category: 'historical_records',
        notes: 'Animal deceased - transfer to historical records'
      };
    }

    // Check sold status
    if (animal.status === 'sold') {
      return {
        shouldTransfer: true,
        reason: 'sold',
        priority: 'high',
        category: 'sales_records',
        notes: 'Animal sold - transfer to sales inventory'
      };
    }

    // Check retirement age
    if (ageInYears >= this.transferCriteria.retirement.age) {
      return {
        shouldTransfer: true,
        reason: 'retirement_age',
        priority: 'medium',
        category: 'retirement_inventory',
        notes: `Animal reached retirement age (${ageInYears.toFixed(1)} years)`
      };
    }

    // Check breeding cycles (if applicable)
    const breedingCount = animal.genealogy?.breedingHistory?.length || 0;
    if (breedingCount >= this.transferCriteria.retirement.breedingCycles) {
      return {
        shouldTransfer: true,
        reason: 'breeding_limit',
        priority: 'medium',
        category: 'retirement_inventory',
        notes: `Animal reached breeding limit (${breedingCount} cycles)`
      };
    }

    // Check market readiness for sale
    const marketCriteria = this.transferCriteria.sale;
    const targetWeight = marketCriteria.marketWeight[animal.species] || 0;
    const optimalAge = marketCriteria.optimalAge[animal.species] || 24;

    if (animal.weight >= targetWeight && ageInMonths >= optimalAge) {
      return {
        shouldTransfer: false, // Don't auto-transfer, but flag for consideration
        reason: 'market_ready',
        priority: 'low',
        category: 'market_candidates',
        notes: `Animal ready for market (${animal.weight}kg, ${ageInMonths} months)`
      };
    }

    return {
      shouldTransfer: false,
      reason: 'active',
      priority: 'none',
      category: 'active_livestock',
      notes: 'Animal in active lifecycle stage'
    };
  }

  /**
   * Update lifecycle stages for all animals
   */
  async updateAllLifecycleStages() {
    try {
      logger.info('Starting bulk lifecycle stage update...');
      
      const animals = await Animal.find({
        status: { $in: ['active', 'breeding'] },
        birthDate: { $exists: true, $ne: null }
      });

      const updates = [];
      const transferCandidates = [];

      for (const animal of animals) {
        const ageInMonths = this.calculateAgeInMonths(animal.birthDate);
        const expectedStage = this.getExpectedLifecycleStage(ageInMonths, animal.species);
        const currentStage = animal.lifecycle?.currentStage;

        // Check for stage transition
        if (expectedStage !== currentStage) {
          const stageUpdate = {
            updateOne: {
              filter: { _id: animal._id },
              update: {
                $set: {
                  'lifecycle.currentStage': expectedStage,
                  updatedAt: new Date()
                },
                $push: {
                  'lifecycle.stageHistory': {
                    stage: currentStage || 'unknown',
                    startDate: animal.lifecycle?.stageHistory?.length > 0 ? 
                      animal.lifecycle.stageHistory[animal.lifecycle.stageHistory.length - 1].endDate : 
                      animal.birthDate,
                    endDate: new Date(),
                    autoTransitioned: true,
                    notes: `Auto-transition from ${currentStage || 'unknown'} to ${expectedStage} (age: ${ageInMonths} months)`
                  }
                }
              }
            }
          };
          updates.push(stageUpdate);
        }

        // Check for inventory transfer
        const transferCheck = this.shouldTransferToInventory(animal);
        if (transferCheck.shouldTransfer) {
          transferCandidates.push({
            animalId: animal._id,
            tagNumber: animal.tagNumber,
            name: animal.name,
            ...transferCheck
          });
        }
      }

      // Execute bulk updates
      let updateResult = { modifiedCount: 0 };
      if (updates.length > 0) {
        updateResult = await Animal.bulkWrite(updates);
        logger.info(`Updated lifecycle stages for ${updateResult.modifiedCount} animals`);
      }

      // Log transfer candidates
      if (transferCandidates.length > 0) {
        logger.info(`Found ${transferCandidates.length} animals for potential inventory transfer`);
        for (const candidate of transferCandidates) {
          logger.info(`Transfer candidate: ${candidate.tagNumber} - ${candidate.reason} (${candidate.priority} priority)`);
        }
      }

      return {
        success: true,
        updatedCount: updateResult.modifiedCount,
        transferCandidates,
        message: `Processed ${animals.length} animals, updated ${updateResult.modifiedCount} lifecycle stages`
      };

    } catch (error) {
      logger.error('Error updating lifecycle stages:', error);
      throw error;
    }
  }

  /**
   * Get animals by lifecycle stage
   */
  async getAnimalsByLifecycleStage(stage) {
    try {
      const animals = await Animal.find({
        'lifecycle.currentStage': stage,
        status: { $in: ['active', 'breeding'] }
      }).sort({ birthDate: 1 });

      return animals.map(animal => ({
        id: animal._id,
        tagNumber: animal.tagNumber,
        name: animal.name,
        species: animal.species,
        breed: animal.breed,
        age: this.calculateAgeInMonths(animal.birthDate),
        currentStage: animal.lifecycle?.currentStage,
        transferStatus: this.shouldTransferToInventory(animal)
      }));
    } catch (error) {
      logger.error(`Error getting animals by lifecycle stage ${stage}:`, error);
      throw error;
    }
  }

  /**
   * Get transfer candidates
   */
  async getTransferCandidates() {
    try {
      const animals = await Animal.find({
        status: { $in: ['active', 'breeding', 'sold', 'deceased'] },
        birthDate: { $exists: true, $ne: null }
      });

      const candidates = [];
      for (const animal of animals) {
        const transferCheck = this.shouldTransferToInventory(animal);
        if (transferCheck.shouldTransfer || transferCheck.reason === 'market_ready') {
          candidates.push({
            id: animal._id,
            tagNumber: animal.tagNumber,
            name: animal.name,
            species: animal.species,
            age: this.calculateAgeInMonths(animal.birthDate),
            weight: animal.weight,
            ...transferCheck
          });
        }
      }

      // Sort by priority
      const priorityOrder = { immediate: 0, high: 1, medium: 2, low: 3, none: 4 };
      candidates.sort((a, b) => priorityOrder[a.priority] - priorityOrder[b.priority]);

      return candidates;
    } catch (error) {
      logger.error('Error getting transfer candidates:', error);
      throw error;
    }
  }

  /**
   * Execute inventory transfer for an animal
   */
  async executeInventoryTransfer(animalId, transferReason, notes = '') {
    try {
      const animal = await Animal.findById(animalId);
      if (!animal) {
        throw new Error('Animal not found');
      }

      // Update animal status
      const updateData = {
        status: transferReason === 'deceased' ? 'deceased' : 'retired',
        updatedAt: new Date(),
        $push: {
          'lifecycle.transferHistory': {
            fromLocation: animal.location,
            toLocation: 'inventory_management',
            transferDate: new Date(),
            reason: transferReason,
            authorizedBy: 'system_auto',
            notes: notes || `Auto-transfer due to ${transferReason}`
          }
        }
      };

      await Animal.findByIdAndUpdate(animalId, updateData);

      // TODO: Create inventory record
      // This would integrate with the inventory management system
      
      logger.info(`Successfully transferred animal ${animal.tagNumber} to inventory (reason: ${transferReason})`);
      
      return {
        success: true,
        animalId,
        tagNumber: animal.tagNumber,
        transferReason,
        message: `Animal ${animal.tagNumber} transferred to inventory`
      };

    } catch (error) {
      logger.error(`Error executing inventory transfer for animal ${animalId}:`, error);
      throw error;
    }
  }

  /**
   * Get lifecycle statistics
   */
  async getLifecycleStatistics() {
    try {
      const pipeline = [
        {
          $match: {
            status: { $in: ['active', 'breeding'] },
            birthDate: { $exists: true, $ne: null }
          }
        },
        {
          $group: {
            _id: '$lifecycle.currentStage',
            count: { $sum: 1 },
            avgAge: { 
              $avg: {
                $divide: [
                  { $subtract: [new Date(), '$birthDate'] },
                  1000 * 60 * 60 * 24 * 30.44 // Convert to months
                ]
              }
            }
          }
        }
      ];

      const stageStats = await Animal.aggregate(pipeline);
      
      const transferCandidates = await this.getTransferCandidates();
      const transferStats = transferCandidates.reduce((acc, candidate) => {
        acc[candidate.reason] = (acc[candidate.reason] || 0) + 1;
        return acc;
      }, {});

      return {
        lifecycleStages: stageStats,
        transferCandidates: transferStats,
        totalAnimals: stageStats.reduce((sum, stage) => sum + stage.count, 0),
        totalTransferCandidates: transferCandidates.length
      };
    } catch (error) {
      logger.error('Error getting lifecycle statistics:', error);
      throw error;
    }
  }
}

module.exports = new LifecycleManagementService();
