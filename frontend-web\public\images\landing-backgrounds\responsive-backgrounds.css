/* Responsive Background Images for Landing Page Tabs */

/* Desktop and large screens */
@media (min-width: 1200px) {
  .Mui<PERSON>ox-root[role="tabpanel"]:nth-of-type(1) {
    background-image: url('/images/landing-backgrounds/high-tech-pasture.jpg');
  }
  .MuiBox-root[role="tabpanel"]:nth-of-type(2) {
    background-image: url('/images/landing-backgrounds/agricultural-technology.jpg');
  }
  .MuiBox-root[role="tabpanel"]:nth-of-type(3) {
    background-image: url('/images/landing-backgrounds/iot-agriculture.png');
  }
  .MuiBox-root[role="tabpanel"]:nth-of-type(4) {
    background-image: url('/images/landing-backgrounds/smart-farming-tech.png');
  }
  .MuiBox-root[role="tabpanel"]:nth-of-type(5) {
    background-image: url('/images/landing-backgrounds/environmental-assessment.jpg');
  }
  .MuiBox-root[role="tabpanel"]:nth-of-type(6) {
    background-image: url('/images/landing-backgrounds/smart-farming-infographics.jpg');
  }
  .MuiBox-root[role="tabpanel"]:nth-of-type(7) {
    background-image: url('/images/landing-backgrounds/iot-in-agriculture.png');
  }
}

/* Tablet screens */
@media (min-width: 768px) and (max-width: 1199px) {
  .MuiBox-root[role="tabpanel"]:nth-of-type(1) {
    background-image: url('/images/landing-backgrounds/high-tech-pasture.jpg');
    background-size: cover;
    background-position: center;
  }
  .MuiBox-root[role="tabpanel"]:nth-of-type(2) {
    background-image: url('/images/landing-backgrounds/agricultural-technology.jpg');
    background-size: cover;
    background-position: center;
  }
  .MuiBox-root[role="tabpanel"]:nth-of-type(3) {
    background-image: url('/images/landing-backgrounds/iot-agriculture.png');
    background-size: cover;
    background-position: center;
  }
  .MuiBox-root[role="tabpanel"]:nth-of-type(4) {
    background-image: url('/images/landing-backgrounds/smart-farming-tech.png');
    background-size: cover;
    background-position: center;
  }
  .MuiBox-root[role="tabpanel"]:nth-of-type(5) {
    background-image: url('/images/landing-backgrounds/environmental-assessment.jpg');
    background-size: cover;
    background-position: center;
  }
  .MuiBox-root[role="tabpanel"]:nth-of-type(6) {
    background-image: url('/images/landing-backgrounds/smart-farming-infographics.jpg');
    background-size: cover;
    background-position: center;
  }
  .MuiBox-root[role="tabpanel"]:nth-of-type(7) {
    background-image: url('/images/landing-backgrounds/iot-in-agriculture.png');
    background-size: cover;
    background-position: center;
  }
}

/* Mobile screens */
@media (max-width: 767px) {
  .MuiBox-root[role="tabpanel"]:nth-of-type(1) {
    background-image: url('/images/landing-backgrounds/high-tech-pasture.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: scroll;
  }
  .MuiBox-root[role="tabpanel"]:nth-of-type(2) {
    background-image: url('/images/landing-backgrounds/agricultural-technology.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: scroll;
  }
  .MuiBox-root[role="tabpanel"]:nth-of-type(3) {
    background-image: url('/images/landing-backgrounds/iot-agriculture.png');
    background-size: cover;
    background-position: center;
    background-attachment: scroll;
  }
  .MuiBox-root[role="tabpanel"]:nth-of-type(4) {
    background-image: url('/images/landing-backgrounds/smart-farming-tech.png');
    background-size: cover;
    background-position: center;
    background-attachment: scroll;
  }
  .MuiBox-root[role="tabpanel"]:nth-of-type(5) {
    background-image: url('/images/landing-backgrounds/environmental-assessment.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: scroll;
  }
  .MuiBox-root[role="tabpanel"]:nth-of-type(6) {
    background-image: url('/images/landing-backgrounds/smart-farming-infographics.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: scroll;
  }
  .MuiBox-root[role="tabpanel"]:nth-of-type(7) {
    background-image: url('/images/landing-backgrounds/iot-in-agriculture.png');
    background-size: cover;
    background-position: center;
    background-attachment: scroll;
  }
}

