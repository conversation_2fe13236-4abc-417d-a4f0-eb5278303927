/**
 * Data Integration Verification Script
 * 
 * Verifies that all data relationships and foreign key references are correctly established
 */

const { MongoClient, ObjectId } = require('mongodb');
require('dotenv').config();

// MongoDB connection
const uri = process.env.MONGODB_URI || "mongodb+srv://AMPD:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0";
const dbName = process.env.MONGODB_DB_NAME || 'ampd_livestock';

console.log('🔍 Starting Data Integration Verification...');
console.log(`Database: ${dbName}`);

async function verifyDataIntegration() {
  let client;
  
  try {
    console.log('🔗 Connecting to MongoDB...');
    client = new MongoClient(uri);
    
    await client.connect();
    console.log('✅ Connected successfully to MongoDB');
    
    const db = client.db(dbName);
    
    // Get all collections
    const animals = await db.collection('animals').find({}).toArray();
    const healthRecords = await db.collection('health_records').find({}).toArray();
    const feedingRecords = await db.collection('feeding_records').find({}).toArray();
    const financialRecords = await db.collection('financial_records').find({}).toArray();
    
    console.log('\n📊 COLLECTION COUNTS:');
    console.log('====================');
    console.log(`Animals: ${animals.length} records`);
    console.log(`Health Records: ${healthRecords.length} records`);
    console.log(`Feeding Records: ${feedingRecords.length} records`);
    console.log(`Financial Records: ${financialRecords.length} records`);
    
    // Verify animal data structure
    console.log('\n🐄 ANIMAL DATA VERIFICATION:');
    console.log('============================');
    
    let animalsWithGenealogy = 0;
    let animalsWithLifecycle = 0;
    let animalsWithIdentification = 0;
    let animalsWithPhysicalChars = 0;
    
    animals.forEach((animal, index) => {
      console.log(`\nAnimal ${index + 1}: ${animal.tagNumber} (${animal.name})`);
      console.log(`  Species: ${animal.species}, Breed: ${animal.breed}, Gender: ${animal.gender}`);
      console.log(`  Birth Date: ${animal.birthDate ? new Date(animal.birthDate).toLocaleDateString() : 'Not set'}`);
      console.log(`  Weight: ${animal.weight}kg, Location: ${animal.location}`);
      
      // Check genealogy
      if (animal.genealogy) {
        animalsWithGenealogy++;
        console.log(`  ✅ Genealogy: Sire: ${animal.genealogy.sire?.name || 'Not set'}, Dam: ${animal.genealogy.dam?.name || 'Not set'}`);
        console.log(`  ✅ Bloodline: ${animal.genealogy.geneticTraits?.bloodline || 'Not set'}`);
      } else {
        console.log(`  ❌ Missing genealogy information`);
      }
      
      // Check lifecycle
      if (animal.lifecycle) {
        animalsWithLifecycle++;
        console.log(`  ✅ Lifecycle: Current stage: ${animal.lifecycle.currentStage}`);
        console.log(`  ✅ Stage history: ${animal.lifecycle.stageHistory?.length || 0} entries`);
      } else {
        console.log(`  ❌ Missing lifecycle information`);
      }
      
      // Check identification
      if (animal.identification) {
        animalsWithIdentification++;
        console.log(`  ✅ Identification: RFID: ${animal.identification.rfidTag}, Registration: ${animal.identification.registrationNumber}`);
      } else {
        console.log(`  ❌ Missing identification information`);
      }
      
      // Check physical characteristics
      if (animal.physicalCharacteristics) {
        animalsWithPhysicalChars++;
        console.log(`  ✅ Physical: Height: ${animal.physicalCharacteristics.height?.value}${animal.physicalCharacteristics.height?.unit}, Color: ${animal.physicalCharacteristics.colorMarkings?.primaryColor}`);
      } else {
        console.log(`  ❌ Missing physical characteristics`);
      }
    });
    
    // Verify health records relationships
    console.log('\n🏥 HEALTH RECORDS VERIFICATION:');
    console.log('===============================');
    
    let linkedHealthRecords = 0;
    let orphanedHealthRecords = 0;
    
    for (const healthRecord of healthRecords) {
      const linkedAnimal = animals.find(animal => 
        animal._id.toString() === healthRecord.animalId.toString() ||
        animal.tagNumber === healthRecord.animalTagNumber
      );
      
      if (linkedAnimal) {
        linkedHealthRecords++;
        console.log(`✅ Health record for ${healthRecord.animalTagNumber}: ${healthRecord.recordType} - ${healthRecord.diagnosis}`);
      } else {
        orphanedHealthRecords++;
        console.log(`❌ Orphaned health record: ${healthRecord.animalTagNumber} (animal not found)`);
      }
    }
    
    // Verify feeding records relationships
    console.log('\n🌾 FEEDING RECORDS VERIFICATION:');
    console.log('================================');
    
    let linkedFeedingRecords = 0;
    let orphanedFeedingRecords = 0;
    
    for (const feedingRecord of feedingRecords) {
      const linkedAnimal = animals.find(animal => 
        animal._id.toString() === feedingRecord.animalId.toString() ||
        animal.tagNumber === feedingRecord.animalTagNumber
      );
      
      if (linkedAnimal) {
        linkedFeedingRecords++;
        console.log(`✅ Feeding record for ${feedingRecord.animalTagNumber}: ${feedingRecord.feedType} - ${feedingRecord.quantity}${feedingRecord.unit}`);
      } else {
        orphanedFeedingRecords++;
        console.log(`❌ Orphaned feeding record: ${feedingRecord.animalTagNumber} (animal not found)`);
      }
    }
    
    // Verify financial records
    console.log('\n💰 FINANCIAL RECORDS VERIFICATION:');
    console.log('===================================');
    
    let incomeRecords = 0;
    let expenseRecords = 0;
    let linkedFinancialRecords = 0;
    
    for (const financialRecord of financialRecords) {
      if (financialRecord.type === 'income') {
        incomeRecords++;
      } else if (financialRecord.type === 'expense') {
        expenseRecords++;
      }
      
      if (financialRecord.relatedAnimalId) {
        const linkedAnimal = animals.find(animal => 
          animal._id.toString() === financialRecord.relatedAnimalId.toString()
        );
        if (linkedAnimal) {
          linkedFinancialRecords++;
        }
      }
      
      console.log(`${financialRecord.type === 'income' ? '💰' : '💸'} ${financialRecord.type.toUpperCase()}: ${financialRecord.category} - R${financialRecord.amount}`);
    }
    
    // Calculate statistics
    console.log('\n📈 VERIFICATION STATISTICS:');
    console.log('===========================');
    console.log(`Animals with genealogy: ${animalsWithGenealogy}/${animals.length} (${Math.round(animalsWithGenealogy/animals.length*100)}%)`);
    console.log(`Animals with lifecycle: ${animalsWithLifecycle}/${animals.length} (${Math.round(animalsWithLifecycle/animals.length*100)}%)`);
    console.log(`Animals with identification: ${animalsWithIdentification}/${animals.length} (${Math.round(animalsWithIdentification/animals.length*100)}%)`);
    console.log(`Animals with physical characteristics: ${animalsWithPhysicalChars}/${animals.length} (${Math.round(animalsWithPhysicalChars/animals.length*100)}%)`);
    console.log(`Linked health records: ${linkedHealthRecords}/${healthRecords.length} (${Math.round(linkedHealthRecords/healthRecords.length*100)}%)`);
    console.log(`Orphaned health records: ${orphanedHealthRecords}`);
    console.log(`Linked feeding records: ${linkedFeedingRecords}/${feedingRecords.length} (${Math.round(linkedFeedingRecords/feedingRecords.length*100)}%)`);
    console.log(`Orphaned feeding records: ${orphanedFeedingRecords}`);
    console.log(`Income records: ${incomeRecords}`);
    console.log(`Expense records: ${expenseRecords}`);
    console.log(`Animal-linked financial records: ${linkedFinancialRecords}`);
    
    // Overall verification status
    const overallScore = (
      (animalsWithGenealogy/animals.length) +
      (animalsWithLifecycle/animals.length) +
      (animalsWithIdentification/animals.length) +
      (animalsWithPhysicalChars/animals.length) +
      (linkedHealthRecords/healthRecords.length) +
      (linkedFeedingRecords/feedingRecords.length)
    ) / 6 * 100;
    
    console.log('\n🎯 OVERALL VERIFICATION SCORE:');
    console.log('==============================');
    console.log(`Data Integration Score: ${Math.round(overallScore)}%`);
    
    if (overallScore >= 95) {
      console.log('🎉 EXCELLENT: Data integration is comprehensive and well-structured!');
    } else if (overallScore >= 80) {
      console.log('✅ GOOD: Data integration is solid with minor gaps.');
    } else if (overallScore >= 60) {
      console.log('⚠️ FAIR: Data integration needs improvement.');
    } else {
      console.log('❌ POOR: Significant data integration issues detected.');
    }
    
    // Test some complex queries
    console.log('\n🔍 COMPLEX QUERY TESTING:');
    console.log('=========================');
    
    // Test aggregation pipeline
    const animalHealthSummary = await db.collection('health_records').aggregate([
      {
        $group: {
          _id: '$animalTagNumber',
          recordCount: { $sum: 1 },
          totalCost: { $sum: '$cost' },
          recordTypes: { $addToSet: '$recordType' }
        }
      }
    ]).toArray();
    
    console.log('Animal Health Summary:');
    animalHealthSummary.forEach(summary => {
      console.log(`  ${summary._id}: ${summary.recordCount} records, R${summary.totalCost} total cost, Types: ${summary.recordTypes.join(', ')}`);
    });
    
    // Test feeding cost analysis
    const feedingCostSummary = await db.collection('feeding_records').aggregate([
      {
        $group: {
          _id: '$animalTagNumber',
          totalFeedCost: { $sum: '$cost' },
          totalQuantity: { $sum: '$quantity' },
          feedTypes: { $addToSet: '$feedType' }
        }
      }
    ]).toArray();
    
    console.log('\nFeeding Cost Summary:');
    feedingCostSummary.forEach(summary => {
      console.log(`  ${summary._id}: R${summary.totalFeedCost} total cost, ${summary.totalQuantity}kg total feed, Types: ${summary.feedTypes.join(', ')}`);
    });
    
    console.log('\n✅ DATA INTEGRATION VERIFICATION COMPLETED!');
    
    return {
      success: true,
      statistics: {
        animals: animals.length,
        healthRecords: healthRecords.length,
        feedingRecords: feedingRecords.length,
        financialRecords: financialRecords.length,
        overallScore: Math.round(overallScore),
        linkedHealthRecords,
        linkedFeedingRecords,
        orphanedRecords: orphanedHealthRecords + orphanedFeedingRecords
      }
    };
    
  } catch (error) {
    console.error('❌ Error verifying data integration:', error);
    throw error;
  } finally {
    if (client) {
      await client.close();
      console.log('\n🔌 MongoDB connection closed');
    }
  }
}

// Run the verification
verifyDataIntegration().catch(console.error);
