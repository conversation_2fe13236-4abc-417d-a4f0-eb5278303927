import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import AgriIntelLogo from './common/AgriIntelLogo';

const Navbar: React.FC = () => {
  return (
    <motion.nav
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="landing-nav"
    >
      <div className="nav-brand">
        <Link to="/" style={{ textDecoration: 'none' }}>
          <AgriIntelLogo
            variant="full"
            size="large"
            theme="agriIntel"
            showTagline={true}
            orientation="horizontal"
          />
        </Link>
      </div>
      <div className="nav-links">
        <Link to="/#features" className="nav-link">Features</Link>
        <Link to="/#testimonials" className="nav-link">Testimonials</Link>
        <Link to="/#contact" className="nav-link">Contact</Link>
        <Link to="/animals" className="nav-link">Animals</Link>
        <Link to="/feed-and-financials" className="nav-link">Feed & Financials</Link>
        <Link to="/partners-and-resources" className="nav-link">Partners & Resources</Link>
      </div>
      <div className="nav-actions">
        <Link to="/login" className="btn btn-secondary">Login</Link>
        <Link to="/register" className="btn btn-primary">Sign Up</Link>
      </div>
    </motion.nav>
  );
};

export default Navbar;