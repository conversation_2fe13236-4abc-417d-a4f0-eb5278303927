import React, { useState, useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import Navbar from './Navbar';
import Sidebar from './Sidebar';
import { motion } from 'framer-motion';
import '../styles/backgrounds.css';
import '../styles/animations.css';
import '../styles/dramatic-agriintel-theme.css';
import styles from './DashboardLayout.module.css';
import { useMediaQuery } from '@mui/material';
import { OfflineIndicator, MongoDbStatus } from './common';
import { useThemeContext } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';

const DashboardLayout: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const { theme } = useThemeContext();
  const { translate } = useLanguage();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // No need for getCurrentModule anymore as we're using solid colors

  useEffect(() => {
    // Simulate loading resources
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 800);

    return () => clearTimeout(timer);
  }, []);

  // Close sidebar on mobile by default
  useEffect(() => {
    setSidebarOpen(!isMobile);
  }, [isMobile]);

  // Toggle sidebar function
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  // No need to update background image anymore as we're using solid colors

  return (
    <div className="flex h-screen">
      {isLoading ? (
        <div className="fixed inset-0 flex items-center justify-center bg-white z-50">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-primary-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <h2 className="text-xl font-semibold text-primary-800">{translate('common.loading')} {translate('dashboard.welcome')}</h2>
            <p className="text-gray-600">{translate('dashboard.preparing')}</p>
          </div>
        </div>
      ) : (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="flex w-full h-screen"
        >
          <Sidebar isOpen={sidebarOpen} onToggle={toggleSidebar} isMobile={isMobile} />
          <div className="flex-1 flex flex-col overflow-hidden">
            <Navbar onMenuClick={toggleSidebar} />
            <main
              className="flex-1 overflow-x-hidden overflow-y-auto custom-scrollbar relative"
            >
              {/* Enhanced AgriIntel Metal Lime Background */}
              <div
                className={`absolute inset-0 z-0 transition-all duration-1000 ease-in-out ${styles.agriIntelBackground}`}
              />
              {/* Enhanced Gradient Overlay with Animation */}
              <div
                className={`absolute inset-0 z-0 transition-all duration-1000 ease-in-out ${styles.gradientOverlay}`}
              />
              {/* Enhanced Pattern Overlay */}
              <div
                className={`absolute inset-0 z-0 ${styles.patternOverlay}`}
              />
              {/* Particle Effect Overlay */}
              <div
                className={`absolute inset-0 z-0 overflow-hidden ${styles.particleOverlay}`}
              />
              <div className="min-h-full relative z-10">
                <Outlet />
              </div>
              {/* Status Indicators */}
              <div className="fixed bottom-4 left-4 z-50 flex flex-col gap-2">
                <OfflineIndicator />
                <MongoDbStatus />
              </div>
            </main>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default DashboardLayout;