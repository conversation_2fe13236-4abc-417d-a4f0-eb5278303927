import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import {
  Typography,
  TextField,
  Button,
  Alert,
  InputAdornment,
  IconButton,
  CircularProgress,
  Chip
} from '@mui/material';
import {
  Person,
  Lock,
  Visibility,
  VisibilityOff,
  Agriculture,
  Star,
  Verified
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import EnhancedLanguageSelector from '../components/common/EnhancedLanguageSelector';
import AgriIntelLogo from '../components/common/AgriIntelLogo';
import '../styles/beta-tier.css';
import '../styles/professional-tier.css';
import '../styles/split-screen-login.css';

const Login: React.FC = () => {
  const [loginTier, setLoginTier] = useState<'beta' | 'professional'>('beta');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');

  const { login, isLoading, error: authError } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Check URL parameters to determine login tier
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const tier = searchParams.get('tier');
    if (tier === 'professional' || tier === 'pro') {
      setLoginTier('professional');
    } else if (tier === 'beta') {
      setLoginTier('beta');
    } else {
      // Default to beta for backward compatibility
      setLoginTier('beta');
    }
  }, [location.search]);

  useEffect(() => {
    if (authError) {
      setError(authError);
    }
  }, [authError]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!username || !password) {
      setError('Please enter both username and password');
      return;
    }
    try {
      await login(username, password);

      // Get user from localStorage after successful login
      const userData = JSON.parse(localStorage.getItem('user') || '{}');
      const redirectPath = location.state?.from?.pathname || '/dashboard';

      // Redirect based on user role
      if (userData.role === 'beta') {
        navigate('/beta-dashboard', { replace: true });
      } else if (userData.role === 'professional' || userData.role === 'admin') {
        navigate(redirectPath, { replace: true });
      } else {
        navigate('/dashboard', { replace: true });
      }
    } catch (err) {
      setError('Invalid credentials. Please try again.');
    }
  };

  const handleTogglePassword = () => {
    setShowPassword(!showPassword);
  };

  // Tier configuration only

  const tierConfig = {
    beta: {
      title: 'AgriIntel BETA V1',
      subtitle: 'Start your free trial with our BETA V1 Starter package',
      badgeText: 'BETA V1 Starter',
      badgeIcon: <Star />,
      primaryColor: '#FF9800',
      features: [
        '• Animal Management (up to 50 animals)',
        '• Basic Health Monitoring',
        '• Feed Recording & Tracking',
        '• Financial Overview',
        '• Basic Settings & Configuration'
      ],
      credentials: 'Demo/123',
      upgradeText: 'Upgrade to Pro V2 - R699/month'
    },
    professional: {
      title: 'AgriIntel Professional V2',
      subtitle: 'Sign in to your Pro V2 account',
      badgeText: 'Pro V2',
      badgeIcon: <Verified />,
      primaryColor: '#4CAF50',
      features: [
        '• All 12 modules with full functionality',
        '• AI-powered predictive analytics',
        '• Professional marketplace access',
        '• Priority support & training'
      ],
      credentials: 'Pro/123 or admin/Admin@123'
    }
  };

  const config = tierConfig[loginTier];

  return (
    <div className={`split-screen-login ${loginTier}`}>
      {/* Language Selector */}
      <div className="login-language-selector">
        <EnhancedLanguageSelector
          variant="futuristic"
          showLabel={false}
          showFlag={true}
          size="medium"
        />
      </div>

      {/* Left Panel - Image */}
      <div className="login-image-panel">
        <div className="login-image-overlay">
          <AgriIntelLogo
            variant="full"
            size="extra-large"
            theme="white"
            showTagline={true}
            orientation="vertical"
          />
          <div className="login-tier-badge">
            <Chip
              label={config.badgeText}
              sx={{
                backgroundColor: config.primaryColor,
                color: 'white',
                fontWeight: 'bold',
                fontSize: '1.1rem',
                padding: '8px 16px',
                marginTop: '20px'
              }}
              icon={config.badgeText === 'BETA V1' ? <Star /> : <Verified />}
            />
          </div>
        </div>
      </div>

      {/* Right Panel - Form */}
      <div className="login-form-panel">
        <div className="login-form-container">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            {/* Tier Branding */}
            <Chip
              label={config.badgeText}
              className="login-brand-badge"
              icon={config.badgeIcon}
            />
            <Typography variant="h4" className="login-form-title">
              {config.title}
            </Typography>
            <Typography variant="body1" className="login-form-subtitle">
              {config.subtitle}
            </Typography>

              {/* Features Preview */}
            <div className="login-features-box">
              <div className="login-features-title">
                🎯 {config.badgeText} Features:
              </div>
              <div className="login-features-list">
                {config.features.map((feature, index) => (
                  <div key={index}>{feature}</div>
                ))}
              </div>
            </div>

            {/* Login Form */}
            <form onSubmit={handleLogin}>
              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}

              <TextField
                fullWidth
                label="Username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="login-form-field"
                required
                autoComplete="username"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Person sx={{ color: config.primaryColor }} />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '&:hover fieldset': {
                      borderColor: config.primaryColor,
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: config.primaryColor,
                    },
                  },
                  '& .MuiInputLabel-root.Mui-focused': {
                    color: config.primaryColor,
                  },
                }}
              />

              <TextField
                fullWidth
                label="Password"
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="login-form-field"
                required
                autoComplete="current-password"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Lock sx={{ color: config.primaryColor }} />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={handleTogglePassword}
                        edge="end"
                        sx={{ color: config.primaryColor }}
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '&:hover fieldset': {
                      borderColor: config.primaryColor,
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: config.primaryColor,
                    },
                  },
                  '& .MuiInputLabel-root.Mui-focused': {
                    color: config.primaryColor,
                  },
                }}
              />

              <Button
                type="submit"
                variant="contained"
                disabled={isLoading}
                className="login-primary-button"
                startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : <Agriculture />}
              >
                {isLoading ? 'Signing In...' : `Access ${config.badgeText} Dashboard`}
              </Button>
            </form>

            {/* Demo Credentials */}
            <div className="login-credentials-box">
              <div className="login-credentials-title">
                🔑 Demo Credentials:
              </div>
              <div className="login-credentials-text">
                {config.credentials}
              </div>
            </div>

            {/* Upgrade Prompt for BETA */}
            {loginTier === 'beta' && (
              <div className="login-upgrade-prompt">
                <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
                  Need more features?
                </Typography>
                <Button
                  variant="outlined"
                  size="small"
                  sx={{
                    borderColor: '#FF9800',
                    color: '#FF9800',
                    '&:hover': {
                      borderColor: '#F57C00',
                      backgroundColor: 'rgba(255, 152, 0, 0.1)',
                    },
                  }}
                  onClick={() => navigate('/pricing')}
                >
                  Upgrade to Pro V2 - R699/month
                </Button>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default Login;
