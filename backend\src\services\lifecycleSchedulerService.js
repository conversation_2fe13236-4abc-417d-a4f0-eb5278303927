/**
 * Lifecycle Scheduler Service
 * 
 * This service handles scheduled tasks for automated lifecycle management,
 * including daily updates, monthly reviews, and automated transfers.
 */

const cron = require('node-cron');
const lifecycleManagementService = require('./lifecycleManagementService');
const logger = require('../utils/logger');

class LifecycleSchedulerService {
  constructor() {
    this.scheduledTasks = new Map();
    this.isInitialized = false;
  }

  /**
   * Initialize all scheduled tasks
   */
  initialize() {
    if (this.isInitialized) {
      logger.warn('Lifecycle scheduler already initialized');
      return;
    }

    try {
      // Daily lifecycle stage updates (runs at 2 AM every day)
      this.scheduleDailyLifecycleUpdate();
      
      // Weekly transfer candidate review (runs every Monday at 6 AM)
      this.scheduleWeeklyTransferReview();
      
      // Monthly comprehensive lifecycle audit (runs on 1st of each month at 3 AM)
      this.scheduleMonthlyLifecycleAudit();
      
      // Hourly quick health status check (runs every hour during business hours)
      this.scheduleHourlyHealthCheck();

      this.isInitialized = true;
      logger.info('Lifecycle scheduler initialized successfully');
    } catch (error) {
      logger.error('Error initializing lifecycle scheduler:', error);
      throw error;
    }
  }

  /**
   * Schedule daily lifecycle stage updates
   */
  scheduleDailyLifecycleUpdate() {
    const task = cron.schedule('0 2 * * *', async () => {
      try {
        logger.info('Starting daily lifecycle stage update...');
        const result = await lifecycleManagementService.updateAllLifecycleStages();
        logger.info(`Daily lifecycle update completed: ${result.message}`);
        
        // Log any high-priority transfer candidates
        const highPriorityTransfers = result.transferCandidates.filter(
          candidate => candidate.priority === 'immediate' || candidate.priority === 'high'
        );
        
        if (highPriorityTransfers.length > 0) {
          logger.warn(`Found ${highPriorityTransfers.length} high-priority transfer candidates`);
          for (const candidate of highPriorityTransfers) {
            logger.warn(`High priority: ${candidate.tagNumber} - ${candidate.reason}`);
          }
        }
      } catch (error) {
        logger.error('Error in daily lifecycle update:', error);
      }
    }, {
      scheduled: false,
      timezone: 'Africa/Johannesburg'
    });

    this.scheduledTasks.set('dailyLifecycleUpdate', task);
    task.start();
    logger.info('Scheduled daily lifecycle updates at 2:00 AM');
  }

  /**
   * Schedule weekly transfer candidate review
   */
  scheduleWeeklyTransferReview() {
    const task = cron.schedule('0 6 * * 1', async () => {
      try {
        logger.info('Starting weekly transfer candidate review...');
        const candidates = await lifecycleManagementService.getTransferCandidates();
        
        // Group candidates by priority and reason
        const summary = candidates.reduce((acc, candidate) => {
          const key = `${candidate.priority}_${candidate.reason}`;
          if (!acc[key]) {
            acc[key] = { priority: candidate.priority, reason: candidate.reason, count: 0, animals: [] };
          }
          acc[key].count++;
          acc[key].animals.push(candidate.tagNumber);
          return acc;
        }, {});

        logger.info(`Weekly transfer review completed. Found ${candidates.length} candidates:`);
        Object.values(summary).forEach(group => {
          logger.info(`${group.priority} priority (${group.reason}): ${group.count} animals - ${group.animals.join(', ')}`);
        });

        // Auto-execute immediate transfers
        const immediateTransfers = candidates.filter(c => c.priority === 'immediate');
        for (const candidate of immediateTransfers) {
          try {
            await lifecycleManagementService.executeInventoryTransfer(
              candidate.id,
              candidate.reason,
              'Auto-executed during weekly review due to immediate priority'
            );
            logger.info(`Auto-executed immediate transfer for ${candidate.tagNumber}`);
          } catch (error) {
            logger.error(`Failed to auto-execute transfer for ${candidate.tagNumber}:`, error);
          }
        }

      } catch (error) {
        logger.error('Error in weekly transfer review:', error);
      }
    }, {
      scheduled: false,
      timezone: 'Africa/Johannesburg'
    });

    this.scheduledTasks.set('weeklyTransferReview', task);
    task.start();
    logger.info('Scheduled weekly transfer reviews on Mondays at 6:00 AM');
  }

  /**
   * Schedule monthly comprehensive lifecycle audit
   */
  scheduleMonthlyLifecycleAudit() {
    const task = cron.schedule('0 3 1 * *', async () => {
      try {
        logger.info('Starting monthly comprehensive lifecycle audit...');
        
        // Get comprehensive statistics
        const stats = await lifecycleManagementService.getLifecycleStatistics();
        
        // Log detailed audit results
        logger.info('Monthly Lifecycle Audit Results:');
        logger.info(`Total active animals: ${stats.totalAnimals}`);
        logger.info(`Total transfer candidates: ${stats.totalTransferCandidates}`);
        
        logger.info('Animals by lifecycle stage:');
        stats.lifecycleStages.forEach(stage => {
          logger.info(`  ${stage._id || 'unknown'}: ${stage.count} animals (avg age: ${stage.avgAge?.toFixed(1) || 'N/A'} months)`);
        });
        
        logger.info('Transfer candidates by reason:');
        Object.entries(stats.transferCandidates).forEach(([reason, count]) => {
          logger.info(`  ${reason}: ${count} animals`);
        });

        // Update all lifecycle stages as part of monthly audit
        const updateResult = await lifecycleManagementService.updateAllLifecycleStages();
        logger.info(`Monthly audit update: ${updateResult.message}`);

      } catch (error) {
        logger.error('Error in monthly lifecycle audit:', error);
      }
    }, {
      scheduled: false,
      timezone: 'Africa/Johannesburg'
    });

    this.scheduledTasks.set('monthlyLifecycleAudit', task);
    task.start();
    logger.info('Scheduled monthly lifecycle audits on 1st of each month at 3:00 AM');
  }

  /**
   * Schedule hourly health status check during business hours
   */
  scheduleHourlyHealthCheck() {
    const task = cron.schedule('0 8-17 * * 1-5', async () => {
      try {
        // Quick check for any animals that need immediate attention
        const candidates = await lifecycleManagementService.getTransferCandidates();
        const immediateAttention = candidates.filter(c => 
          c.priority === 'immediate' || 
          (c.reason === 'deceased' && c.priority === 'high')
        );

        if (immediateAttention.length > 0) {
          logger.warn(`Hourly check: ${immediateAttention.length} animals need immediate attention`);
          immediateAttention.forEach(animal => {
            logger.warn(`Immediate attention: ${animal.tagNumber} - ${animal.reason}`);
          });
        }
      } catch (error) {
        logger.error('Error in hourly health check:', error);
      }
    }, {
      scheduled: false,
      timezone: 'Africa/Johannesburg'
    });

    this.scheduledTasks.set('hourlyHealthCheck', task);
    task.start();
    logger.info('Scheduled hourly health checks during business hours (8 AM - 5 PM, Mon-Fri)');
  }

  /**
   * Manual trigger for lifecycle updates (for testing or immediate execution)
   */
  async triggerManualUpdate() {
    try {
      logger.info('Manual lifecycle update triggered...');
      const result = await lifecycleManagementService.updateAllLifecycleStages();
      logger.info(`Manual update completed: ${result.message}`);
      return result;
    } catch (error) {
      logger.error('Error in manual lifecycle update:', error);
      throw error;
    }
  }

  /**
   * Get scheduler status
   */
  getSchedulerStatus() {
    const status = {
      isInitialized: this.isInitialized,
      activeTasks: [],
      nextExecutions: {}
    };

    this.scheduledTasks.forEach((task, name) => {
      status.activeTasks.push({
        name,
        running: task.running,
        scheduled: task.scheduled
      });
    });

    return status;
  }

  /**
   * Stop all scheduled tasks
   */
  stopAllTasks() {
    this.scheduledTasks.forEach((task, name) => {
      task.stop();
      logger.info(`Stopped scheduled task: ${name}`);
    });
    
    this.scheduledTasks.clear();
    this.isInitialized = false;
    logger.info('All lifecycle scheduler tasks stopped');
  }

  /**
   * Restart all scheduled tasks
   */
  restartAllTasks() {
    this.stopAllTasks();
    this.initialize();
    logger.info('All lifecycle scheduler tasks restarted');
  }

  /**
   * Get lifecycle management dashboard data
   */
  async getDashboardData() {
    try {
      const [stats, candidates] = await Promise.all([
        lifecycleManagementService.getLifecycleStatistics(),
        lifecycleManagementService.getTransferCandidates()
      ]);

      // Get animals by stage for detailed breakdown
      const stageBreakdown = {};
      for (const stage of ['calf', 'juvenile', 'adult', 'breeding', 'retirement']) {
        try {
          stageBreakdown[stage] = await lifecycleManagementService.getAnimalsByLifecycleStage(stage);
        } catch (error) {
          logger.error(`Error getting animals for stage ${stage}:`, error);
          stageBreakdown[stage] = [];
        }
      }

      return {
        statistics: stats,
        transferCandidates: candidates,
        stageBreakdown,
        schedulerStatus: this.getSchedulerStatus(),
        lastUpdate: new Date()
      };
    } catch (error) {
      logger.error('Error getting lifecycle dashboard data:', error);
      throw error;
    }
  }
}

module.exports = new LifecycleSchedulerService();
