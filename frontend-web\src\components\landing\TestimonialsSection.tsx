import React from 'react';
import { Box, Typography, Container, Rating, Card, CardContent } from '@mui/material';
import { motion } from 'framer-motion';
import { useLanguage } from '../../contexts/LanguageContext';

interface Testimonial {
  id: string;
  name: string;
  location: string;
  farmType: string;
  rating: number;
  quote: string;
  results: string;
  animalCount: number;
}

const TestimonialsSection: React.FC = () => {
  const { translate } = useLanguage();

  const testimonials: Testimonial[] = [
    {
      id: '1',
      name: '<PERSON>',
      location: 'Bloemfontein, Free State',
      farmType: 'Cattle Ranch',
      rating: 5,
      quote: 'AgriIntel has revolutionized how I manage my 200 head of cattle. The health tracking system caught early signs of disease that saved me thousands of rands.',
      results: '15% reduction in veterinary costs, 20% improvement in breeding success',
      animalCount: 200
    },
    {
      id: '2',
      name: '<PERSON>',
      location: 'Ladysmith, KwaZulu-Natal',
      farmType: 'Mixed Livestock',
      rating: 5,
      quote: 'The feeding management module helped me optimize nutrition for my sheep and goats. My animals are healthier and my feed costs dropped significantly.',
      results: '25% reduction in feed costs, improved wool quality',
      animalCount: 150
    },
    {
      id: '3',
      name: '<PERSON> Botha',
      location: 'Potchefstroom, North West',
      farmType: 'Dairy Farm',
      rating: 5,
      quote: 'The financial tracking in AgriIntel gives me clear insights into profitability per animal. I can make data-driven decisions that boost my bottom line.',
      results: '18% increase in profit margins, better cash flow management',
      animalCount: 80
    },
    {
      id: '4',
      name: 'Nomsa Dlamini',
      location: 'Nelspruit, Mpumalanga',
      farmType: 'Goat Farm',
      rating: 5,
      quote: 'As a new farmer, AgriIntel\'s easy-to-use interface helped me learn proper livestock management. The support team is always ready to help.',
      results: '30% faster record keeping, improved animal health outcomes',
      animalCount: 45
    }
  ];

  return (
    <Box
      component="section"
      sx={{
        py: { xs: 8, md: 12 },
        background: 'linear-gradient(135deg, #F8F9FA 0%, #E8F5E8 100%)',
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      {/* Background Pattern */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: 'url(https://images.unsplash.com/photo-1500595046743-cd271d694d30?ixlib=rb-4.0.3&auto=format&fit=crop&w=2074&q=80)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          opacity: 0.05,
          zIndex: 0
        }}
      />

      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1 }}>
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <Box textAlign="center" mb={8}>
            <Typography
              variant="h2"
              component="h2"
              sx={{
                fontSize: { xs: '2rem', md: '2.5rem' },
                fontWeight: 700,
                color: '#2E7D32',
                mb: 2
              }}
            >
              {translate('landing.testimonials.title', { fallback: 'Trusted by South African Farmers' })}
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: '#666',
                maxWidth: '600px',
                mx: 'auto',
                lineHeight: 1.6
              }}
            >
              {translate('landing.testimonials.subtitle', { 
                fallback: 'See how AgriIntel is helping farmers across South Africa improve their livestock management and increase profitability' 
              })}
            </Typography>
          </Box>
        </motion.div>

        {/* Testimonials Grid */}
        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' },
            gap: 4,
            mb: 6
          }}
        >
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card
                sx={{
                  height: '100%',
                  background: 'rgba(255, 255, 255, 0.95)',
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(46, 125, 50, 0.1)',
                  borderRadius: 3,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 20px 40px rgba(46, 125, 50, 0.15)'
                  }
                }}
              >
                <CardContent sx={{ p: 4 }}>
                  {/* Rating */}
                  <Box mb={2}>
                    <Rating value={testimonial.rating} readOnly size="small" />
                  </Box>

                  {/* Quote */}
                  <Typography
                    variant="body1"
                    sx={{
                      fontStyle: 'italic',
                      color: '#333',
                      lineHeight: 1.6,
                      mb: 3,
                      fontSize: '1rem'
                    }}
                  >
                    "{testimonial.quote}"
                  </Typography>

                  {/* Results */}
                  <Box
                    sx={{
                      p: 2,
                      bgcolor: 'rgba(46, 125, 50, 0.05)',
                      borderRadius: 2,
                      mb: 3,
                      border: '1px solid rgba(46, 125, 50, 0.1)'
                    }}
                  >
                    <Typography
                      variant="body2"
                      sx={{
                        color: '#2E7D32',
                        fontWeight: 600,
                        fontSize: '0.875rem'
                      }}
                    >
                      📈 Results: {testimonial.results}
                    </Typography>
                  </Box>

                  {/* Farmer Info */}
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 56,
                        height: 56,
                        borderRadius: '50%',
                        background: 'linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%)',
                        border: '3px solid #4CAF50',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        fontWeight: 700,
                        fontSize: '1.25rem'
                      }}
                    >
                      {testimonial.name.split(' ').map(n => n[0]).join('')}
                    </Box>
                    <Box>
                      <Typography
                        variant="h6"
                        sx={{
                          fontWeight: 600,
                          color: '#2E7D32',
                          fontSize: '1rem'
                        }}
                      >
                        {testimonial.name}
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{ color: '#666', fontSize: '0.875rem' }}
                      >
                        {testimonial.farmType} • {testimonial.location}
                      </Typography>
                      <Typography
                        variant="caption"
                        sx={{ color: '#4CAF50', fontWeight: 600 }}
                      >
                        {testimonial.animalCount} animals managed
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </Box>

        {/* Trust Statistics */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr 1fr' },
              gap: 4,
              textAlign: 'center',
              mt: 8
            }}
          >
            <Box>
              <Typography
                variant="h3"
                sx={{
                  fontSize: '2.5rem',
                  fontWeight: 800,
                  color: '#4CAF50',
                  mb: 1
                }}
              >
                2,500+
              </Typography>
              <Typography variant="body1" sx={{ color: '#666' }}>
                South African Farmers
              </Typography>
            </Box>
            <Box>
              <Typography
                variant="h3"
                sx={{
                  fontSize: '2.5rem',
                  fontWeight: 800,
                  color: '#4CAF50',
                  mb: 1
                }}
              >
                50,000+
              </Typography>
              <Typography variant="body1" sx={{ color: '#666' }}>
                Animals Tracked
              </Typography>
            </Box>
            <Box>
              <Typography
                variant="h3"
                sx={{
                  fontSize: '2.5rem',
                  fontWeight: 800,
                  color: '#4CAF50',
                  mb: 1
                }}
              >
                15%
              </Typography>
              <Typography variant="body1" sx={{ color: '#666' }}>
                Average Cost Reduction
              </Typography>
            </Box>
          </Box>
        </motion.div>
      </Container>
    </Box>
  );
};

export default TestimonialsSection;
